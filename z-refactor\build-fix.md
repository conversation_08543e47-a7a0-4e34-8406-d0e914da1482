# Build Fix Report

## Error Summary
Total Issues: 164 warnings + 31 errors = 195 issues
- **31 Errors** (blocking build)
- **164 Warnings** (build warnings)

## Error Groups & Fixes

### 1. React Unescaped Entities (31 Errors)
**Issue**: Single quotes (`'`) and double quotes (`"`) in JSX text need to be escaped
**Files affected**: 18 files

**How to fix**:
Replace unescaped quotes with HTML entities:
- `'` → `&apos;` or `&#39;`
- `"` → `&quot;` or `&#34;`

**Example fix**:
```jsx
// Before
<p>Don't click here</p>
<p>He said "Hello"</p>

// After  
<p>Don&apos;t click here</p>
<p>He said &quot;Hello&quot;</p>
```

**Files to fix**:
- `src/app/cashier/dashboard/page.tsx:134`
- `src/app/news/dashboard/articles/[id]/page.tsx:228`
- `src/app/sales/categories/[id]/edit/page.tsx:59` (2 instances)
- `src/app/sales/orders/[id]/page.tsx:80` (2 instances)
- `src/app/sales/products/[id]/edit/page.tsx:59` (2 instances)
- `src/app/settings/page.tsx:861`
- `src/app/shop/orders/page.tsx:60`
- `src/app/shop/orders/[id]/page.tsx:66` (3 instances)
- `src/app/shop/products/[id]/page.tsx:47` (2 instances)
- `src/app/shop/search/page.tsx:39,50` (4 instances)
- `src/components/admin/UserProfileModal.tsx:461`
- `src/components/auth/AuthModal.tsx:291`
- `src/components/auth/EmailVerificationForm.tsx:82`
- `src/components/auth/GeneratedPasswordDisplay.tsx:37`
- `src/components/cashier/CashierDashboardLayout.tsx:117,183`
- `src/components/cashier/MemberSearch.tsx:196` (2 instances)
- `src/components/news/ArticlePreviewModal.tsx:99`
- `src/components/sales/SalesDashboardMain.tsx:116`
- `src/components/volunteer/lead/LeadDashboardStats.tsx:97`
- `src/components/volunteer/public/VolunteerSignupModal.tsx:236,255,275,399` (7 instances)

### 2. Variable Declaration (1 Error)
**Issue**: Using `var` instead of `let` or `const`
**File**: `src/lib/heartbeatService.ts:44`

**How to fix**:
```typescript
// Before
var someVariable = value;

// After
const someVariable = value;
// or
let someVariable = value;
```

### 3. Next.js Image Optimization (36 Warnings)
**Issue**: Using `<img>` instead of Next.js `<Image />` component
**Files affected**: 29 files

**How to fix**:
```jsx
// Before
<img src="/path/image.jpg" alt="description" />

// After
import Image from 'next/image';
<Image src="/path/image.jpg" alt="description" width={100} height={100} />
```

**Files to fix**:
- Admin pages: `featured/page.tsx`, `tickets/page.tsx`, `tickets/[id]/page.tsx`, `users/page.tsx`
- Bank pages: `deposit/page.tsx`, `settings/page.tsx`, `transfer/page.tsx`
- Cashier pages: `dashboard/page.tsx` (3 instances)
- News pages: `articles/page.tsx`, `featured/page.tsx`, `[slug]/page.tsx` (4 instances)
- Components: UserProfileCard, UserProfileModal, MemberCard, MemberProfile, MemberSearch, ArticlePreviewModal, FeaturedImageUploader, NewsArticleCard, NotificationItem, AvatarUploaderV2, DepositReceiptUploader, EventImageUploader, NewsImageUploader, NewsImageUploaderV2, ProductImageUploader, AvatarUploadModal (2 instances), SimpleAvatarUpload, UserMenu, PaymentFilters, PaymentHistory, PaymentList

### 4. React Hook Dependencies (16 Warnings)
**Issue**: Missing dependencies in useEffect/useCallback hooks

**How to fix**:
Add missing dependencies to dependency arrays or use useCallback/useMemo:
```jsx
// Before
useEffect(() => {
  fetchData();
}, []); // Missing 'fetchData' dependency

// After - Option 1: Add dependency
useEffect(() => {
  fetchData();
}, [fetchData]);

// After - Option 2: Wrap in useCallback
const fetchDataCallback = useCallback(() => {
  fetchData();
}, [/* deps */]);

useEffect(() => {
  fetchDataCallback();
}, [fetchDataCallback]);
```

**Files to fix**:
- `src/app/admin/events/new/page.tsx:155`
- `src/app/admin/events/page.tsx:141`
- `src/app/admin/events/[id]/page.tsx:207`
- `src/app/events/page.tsx:148`
- `src/app/events/[id]/page.tsx:98`
- `src/app/shop/checkout/page.tsx:34`
- `src/components/layout/MainLayout.tsx:27`
- `src/components/volunteer/public/UserVolunteerQuickActions.tsx:25`
- `src/contexts/AuthContext.tsx:117`
- `src/contexts/UserStateContext.tsx:88`
- `src/hooks/useSSE.ts:247`
- `src/lib/performance.ts:79`

### 5. TypeScript Non-null Assertions (46 Warnings)
**Issue**: Using forbidden non-null assertion operator (`!`)

**How to fix**:
Replace non-null assertions with proper null checks:
```typescript
// Before
const value = someObject!.property;

// After
const value = someObject?.property;
// or
if (someObject) {
  const value = someObject.property;
}
```

**Files to fix**:
- `src/app/api/bank/cashier/transactions/[id]/route.ts` (14 instances)
- `src/app/api/checkout/webhook/route.ts:22`
- `src/app/news/page.tsx:170`
- `src/lib/heartbeatService.ts` (24 instances)
- `src/lib/stripe-server.ts:4`
- `src/lib/stripe.ts:7`

### 6. Export Patterns (3 Warnings)  
**Issue**: Anonymous default exports should be assigned to variables first

**How to fix**:
```typescript
// Before
export default {
  someMethod: () => {}
};

// After
const hooks = {
  someMethod: () => {}
};
export default hooks;
```

**Files to fix**:
- `src/hooks/useVolunteerPayments.ts:196`
- `src/hooks/useVolunteerShifts.ts:228`
- `src/hooks/useVolunteerUsers.ts:45`

## Priority Fix Order

1. **CRITICAL - Fix all 31 errors first** (unescaped entities + var declaration)
2. **HIGH - React Hook dependencies** (potential runtime issues)
3. **MEDIUM - TypeScript non-null assertions** (type safety)
4. **LOW - Image optimization warnings** (performance improvement)
5. **LOW - Export pattern warnings** (code style)

## Quick Fix Commands

```bash
# From web/apps/main-site directory
# 1. Run build to see current errors
pnpm build

# 2. Fix the build-blocking errors first
# (Manual fixes required for unescaped entities)

# 3. Re-run build after fixes
pnpm build

# 4. Run linting
pnpm lint

# 5. Format code
pnpm format
```

## Notes
- Build will fail until all 31 errors are fixed
- Most warnings won't block the build but should be addressed for code quality
- Consider using ESLint autofix where possible: `npx eslint --fix src/`
- Some React Hook dependency warnings may require careful analysis to avoid infinite re-renders