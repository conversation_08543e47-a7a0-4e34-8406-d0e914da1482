# Volunteer System Check-In Refactorization Plan

## Current System Analysis

### ✅ Working Components
- **Database Schema**: Ready with `VolunteerAssignment.status` field supporting `checked_in`
- **Role-Based Access**: Volunteer Coordinator → Category Lead → Payment workflow properly implemented
- **Category Lead Dashboard**: Functional attendance marking (completed/no-show)
- **Payment Processing**: Complete workflow from Lead verification to Coordinator processing

### ❌ Missing Core Functionality: Time-Based Check-In System

The system **lacks the 15-minute pre-shift check-in functionality** described in requirements:

1. **No automatic check-in availability 15 minutes before shift start**
2. **No client-side check-in interface for volunteers**
3. **No time-based status management**
4. **No "abandoned" status option for Category Leads**

## Required Implementation Plan

### Phase 1: Database & API Foundation

#### 1.1 Status Enhancement
**File:** `web/apps/main-site/prisma/schema.prisma`
- Current status field supports: `pending, confirmed, checked_in, completed, no_show, cancelled`
- **Add:** `abandoned` status for Category Lead use

#### 1.2 New API Endpoints
```typescript
// Client check-in endpoint
POST /api/volunteer/public/shifts/[id]/checkin
GET /api/volunteer/public/shifts/[id]/checkin-status

// Enhanced lead dashboard endpoint  
PATCH /api/volunteer/lead/shifts/[id]/attendance
// Add support for "abandoned" status
```

### Phase 2: Time-Based Check-In Logic

#### 2.1 Check-In Window Calculator
**New Service:** `src/services/volunteerCheckinService.ts`
```typescript
interface CheckInStatus {
  canCheckIn: boolean;
  timeUntilCheckIn?: number; // minutes until check-in opens
  timeUntilExpiry?: number;  // minutes until check-in closes
  windowStart: Date;         // 15 minutes before shift
  windowEnd: Date;           // shift start time
}

export const getCheckInStatus = (shiftStartTime: Date): CheckInStatus
```

#### 2.2 API Implementation
**New File:** `src/app/api/volunteer/public/shifts/[id]/checkin/route.ts`
```typescript
export async function POST(req: NextRequest, { params }: { params: { id: string } }) {
  // 1. Verify user is assigned to shift
  // 2. Check if current time is within 15-minute window
  // 3. Update assignment status to "checked_in"
  // 4. Send notification to Category Lead
  // 5. Return success response
}

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  // Return current check-in status and time windows
}
```

### Phase 3: Frontend Interface Updates

#### 3.1 Volunteer Check-In Interface
**Update:** `src/components/volunteer/public/UserVolunteerQuickActions.tsx`
```typescript
// Add check-in button when within 15-minute window
const CheckInButton = ({ assignment }) => {
  const checkInStatus = useCheckInStatus(assignment.shift.id);
  
  if (checkInStatus.canCheckIn) {
    return (
      <button 
        onClick={() => handleCheckIn(assignment.shift.id)}
        className="bg-green-600 hover:bg-green-700"
      >
        Check In Now
      </button>
    );
  }
  
  if (checkInStatus.timeUntilCheckIn) {
    return (
      <div className="text-gray-400">
        Check-in available in {checkInStatus.timeUntilCheckIn}m
      </div>
    );
  }
};
```

#### 3.2 Category Lead Dashboard Enhancement
**Update:** `src/components/volunteer/lead/ShiftCard.tsx:240-285`
```typescript
// Add "Abandoned" button alongside "Completed" and "No-show"
{assignment.status === "checked_in" && (
  <>
    <button onClick={() => handleStatusChange(assignment.id, "completed")}>
      Completed
    </button>
    <button onClick={() => handleStatusChange(assignment.id, "abandoned")}>
      Abandoned
    </button>
    <button onClick={() => handleStatusChange(assignment.id, "no-show")}>
      No-show
    </button>
  </>
)}
```

### Phase 4: Real-Time Features

#### 4.1 Check-In Notifications
**Integration:** Use existing SSE system (`/api/notifications/sse`)
- Notify volunteers when check-in window opens
- Notify Category Leads when volunteers check in
- Send reminder notifications

#### 4.2 Dashboard Real-Time Updates
**Enhancement:** Existing `LeadDashboardMain.tsx` auto-refresh
- Show real-time check-in status changes
- Display countdown timers for check-in windows

### Phase 5: Enhanced Status Flow

#### Current Flow:
```
pending → assigned → completed/no-show (manual by Category Lead)
```

#### New Flow:
```
pending → assigned → [15min window] → checked_in → completed/abandoned/no-show
                                   ↘ [expired] → no-show (auto)
```

### Implementation Order

1. **Database schema update** (add "abandoned" status support)
2. **Check-in service logic** (time window calculations)
3. **API endpoints** (check-in functionality)
4. **Frontend volunteer interface** (check-in button)
5. **Frontend lead interface** (abandoned status)
6. **Real-time notifications** (SSE integration)
7. **Automated status transitions** (optional: auto no-show on expired check-in)

### Files Requiring Changes

#### Backend
- `prisma/schema.prisma` - Status enum update
- `src/services/volunteerCheckinService.ts` - New service
- `src/app/api/volunteer/public/shifts/[id]/checkin/route.ts` - New endpoint
- `src/app/api/volunteer/lead/shifts/[id]/attendance/route.ts` - Add "abandoned" support

#### Frontend
- `src/components/volunteer/public/UserVolunteerQuickActions.tsx` - Check-in UI
- `src/components/volunteer/lead/ShiftCard.tsx` - Abandoned status button
- `src/hooks/usePublicVolunteer.ts` - Check-in hook
- `src/hooks/useVolunteerLead.ts` - Status update hook

#### Types
- `src/types/volunteer.ts` - Status and check-in interfaces

### Testing Requirements

1. **Time Window Logic**: Verify 15-minute window calculations
2. **Status Transitions**: Test all status change scenarios
3. **Real-Time Updates**: Verify SSE notifications work
4. **Permission Checks**: Ensure role-based access control
5. **Edge Cases**: Handle timezone issues, concurrent check-ins

### Migration Considerations

- **Backward Compatibility**: Existing assignments continue to work
- **Data Migration**: No existing data changes needed
- **Gradual Rollout**: Can be implemented incrementally per phase

## Summary

The existing volunteer system has **excellent foundation** with proper role separation, payment workflow, and database structure. The missing check-in functionality can be added **without major refactoring** by implementing time-based logic and enhancing the existing UI components.

**Estimated Implementation**: 2-3 weeks for full feature set with testing.