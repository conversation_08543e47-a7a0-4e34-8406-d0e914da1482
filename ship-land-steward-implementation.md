# Ships & Land Steward System Implementation Plan

## Overview
This document outlines the phased implementation approach for the Ships management system, including Captain functionality, Land Steward administration, and dynamic forms integration with the existing event system.

## User Role Hierarchy
- **Admin** > **Land Steward** > **Captain** > **Member**
- Users restricted to one ship membership
- Captain applications require Land Steward approval

---

## Phase 1: Core Foundation (MVP) ✅ COMPLETED

### Database Schema Changes ✅ COMPLETED
```sql
-- Ships table
CREATE TABLE Ship (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(255) NOT NULL UNIQUE,
  description TEXT,
  slogan VARCHAR(500),
  logo VARCHAR(255), -- file path to uploaded logo
  tags JSON, -- array of custom tags
  captainId INT NOT NULL,
  status ENUM('active', 'inactive', 'deleted', 'pending_deletion') DEFAULT 'active',
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  <PERSON>OR<PERSON><PERSON><PERSON> (captainId) REFERENCES User(id)
);

-- Ship membership tracking
CREATE TABLE ShipMember (
  id INT PRIMARY KEY AUTO_INCREMENT,
  userId INT NOT NULL,
  shipId INT NOT NULL,
  role VARCHAR(255) DEFAULT 'Member', -- Captain can create custom roles
  status ENUM('active', 'invited', 'left', 'removed') DEFAULT 'invited',
  joinedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  leftAt DATETIME NULL,
  UNIQUE KEY unique_user_ship (userId, shipId),
  FOREIGN KEY (userId) REFERENCES User(id),
  FOREIGN KEY (shipId) REFERENCES Ship(id)
);

-- Captain applications
CREATE TABLE CaptainApplication (
  id INT PRIMARY KEY AUTO_INCREMENT,
  userId INT NOT NULL,
  shipName VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  tags JSON,
  logoPath VARCHAR(255),
  status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
  rejectionReason TEXT NULL,
  previouslyRejected BOOLEAN DEFAULT FALSE,
  appliedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  reviewedAt DATETIME NULL,
  reviewedBy INT NULL,
  FOREIGN KEY (userId) REFERENCES User(id),
  FOREIGN KEY (reviewedBy) REFERENCES User(id)
);

-- Add Land Steward role to User table
ALTER TABLE User ADD COLUMN isLandSteward BOOLEAN DEFAULT FALSE;
```

### API Endpoints 
```
GET /api/ships - List all active ships with search/filter ✅ 
GET /api/ships/[id] - Individual ship details ✅
POST /api/ships/apply - Submit captain application ✅
GET /api/ships/[id]/join - Request to join ship ✅
POST /api/ships/[id]/join/respond - Accept/deny join request ✅

-- Captain-only endpoints 
GET /api/captain/dashboard - Captain dashboard data✅
GET /api/captain/ship - Current ship details for captain✅
PUT /api/captain/ship - Update ship details✅
DELETE /api/captain/ship - Request ship deletion✅

-- Land Steward endpoints ✅ IMPLEMENTED
GET /api/land-steward/applications - Pending captain applications ✅
POST /api/land-steward/applications/[id]/approve - Approve application ✅
POST /api/land-steward/applications/[id]/reject - Reject application ✅
```

### Pages & Components 
```
/ships - Public ships listing with search ✅ 
/ships/[id] - Individual ship page with join button ✅
/ships/apply - Captain application form ✅
/captain/dashboard - Captain management dashboard
/land-steward - Land Steward administration dashboard ✅
/land-steward/applications - Application review interface ✅
---

### Core Features 
- ✅ Ships listing page with card layout
- ✅ Basic search (name, tags, captain username) 
- ✅ Captain application submission
- ✅ Land Steward application approval workflow
- ✅ Ship detail pages with join request functionality
- ✅ Role-based navigation updates (Land Steward moved to avatar dropdown)
- ✅ Image upload for ship logos 
- ✅ Separate approve/reject API endpoints 
- ✅ Authentication fixes for Land Steward system
- ✅ Land Steward directory restructure from /admin to /land-steward
```
---

## Phase 2: Member Management System

### Database Enhancements
```sql
-- Ship roles (custom roles created by captains)
CREATE TABLE ShipRole (
  id INT PRIMARY KEY AUTO_INCREMENT,
  shipId INT NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY unique_ship_role (shipId, name),
  FOREIGN KEY (shipId) REFERENCES Ship(id)
);

-- Update ShipMember to reference roles
ALTER TABLE ShipMember ADD COLUMN roleId INT NULL;
ALTER TABLE ShipMember ADD FOREIGN KEY (roleId) REFERENCES ShipRole(id);

-- Join requests/invitations
CREATE TABLE ShipJoinRequest (
  id INT PRIMARY KEY AUTO_INCREMENT,
  userId INT NOT NULL,
  shipId INT NOT NULL,
  requestedBy INT NOT NULL, -- Captain who sent invite or user who requested
  type ENUM('invite', 'request') NOT NULL,
  status ENUM('pending', 'accepted', 'declined', 'expired') DEFAULT 'pending',
  message TEXT,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  respondedAt DATETIME NULL,
  FOREIGN KEY (userId) REFERENCES User(id),
  FOREIGN KEY (shipId) REFERENCES Ship(id),
  FOREIGN KEY (requestedBy) REFERENCES User(id)
);
```

### API Endpoints
```
-- Member management
GET /api/captain/members - List ship members
POST /api/captain/members/invite - Invite user to ship
DELETE /api/captain/members/[userId] - Remove member
PUT /api/captain/members/[userId]/role - Update member role

-- Role management
GET /api/captain/roles - List ship roles
POST /api/captain/roles - Create new role
PUT /api/captain/roles/[id] - Update role
DELETE /api/captain/roles/[id] - Delete role

-- User responses
GET /api/user/ship-invites - Pending invites for user
POST /api/user/ship-invites/[id]/respond - Accept/decline invite
POST /api/user/ship/leave - Leave current ship
```

### New Components
```
- MemberManagementTable
- RoleCreationForm
- MemberInviteSearch
- JoinRequestNotification
- ShipInviteCard
```

### Features
- Captain member invitation system
- User invite acceptance/decline workflow
- Custom role creation and assignment
- Member removal (Captain and voluntary)
- Member search for invitations
- Role management interface

---

## Phase 3: Advanced Ship Management

### Database Additions
```sql
-- Ship statistics tracking
CREATE TABLE ShipStatistic (
  id INT PRIMARY KEY AUTO_INCREMENT,
  shipId INT NOT NULL,
  memberCount INT DEFAULT 0,
  totalVolunteerHours DECIMAL(10,2) DEFAULT 0,
  totalEventsParticipated INT DEFAULT 0,
  lastUpdated DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (shipId) REFERENCES Ship(id)
);

-- Ship activity log
CREATE TABLE ShipActivity (
  id INT PRIMARY KEY AUTO_INCREMENT,
  shipId INT NOT NULL,
  userId INT NULL,
  action ENUM('member_joined', 'member_left', 'role_changed', 'ship_updated', 'form_submitted') NOT NULL,
  description TEXT,
  metadata JSON, -- Additional action-specific data
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (shipId) REFERENCES Ship(id),
  FOREIGN KEY (userId) REFERENCES User(id)
);


```

### API Endpoints
```
-- Enhanced captain dashboard

POST /api/captain/ship/delete-request - Request ship deletion

-- Land Steward ship management
GET /api/land-steward/ships/[id] - Detailed ship overview
PUT /api/land-steward/ships/[id]/captain - Change ship captain

GET /api/land-steward/export/ships - Export ships data as CSV
```

### Enhanced Features

- Ship deletion request workflow
- Land Steward ship oversight tools
- Captain change functionality
- CSV export for Land Stewards

---

## Phase 4: Dynamic Forms System

### Database Schema
```sql
-- Form templates
CREATE TABLE FormTemplate (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  createdBy INT NOT NULL,
  isReusable BOOLEAN DEFAULT TRUE,
  formStructure JSON NOT NULL, -- Field definitions
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (createdBy) REFERENCES User(id)
);

-- Event-specific forms
CREATE TABLE EventForm (
  id INT PRIMARY KEY AUTO_INCREMENT,
  eventId INT NOT NULL,
  templateId INT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  formStructure JSON NOT NULL,
  status ENUM('draft', 'active', 'closed') DEFAULT 'draft',
  submissionDeadline DATETIME NULL,
  createdBy INT NOT NULL,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (eventId) REFERENCES Event(id),
  FOREIGN KEY (templateId) REFERENCES FormTemplate(id),
  FOREIGN KEY (createdBy) REFERENCES User(id)
);

-- Form submissions
CREATE TABLE FormSubmission (
  id INT PRIMARY KEY AUTO_INCREMENT,
  formId INT NOT NULL,
  shipId INT NOT NULL,
  submittedBy INT NOT NULL,
  submissionData JSON NOT NULL, -- Answers to form fields
  status ENUM('draft', 'submitted', 'reviewed', 'approved', 'rejected') DEFAULT 'submitted',
  reviewNotes TEXT,
  reviewedBy INT NULL,
  submittedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  reviewedAt DATETIME NULL,
  FOREIGN KEY (formId) REFERENCES EventForm(id),
  FOREIGN KEY (shipId) REFERENCES Ship(id),
  FOREIGN KEY (submittedBy) REFERENCES User(id),
  FOREIGN KEY (reviewedBy) REFERENCES User(id)
);

-- Form field types and validation
CREATE TABLE FormField (
  id INT PRIMARY KEY AUTO_INCREMENT,
  formId INT NOT NULL,
  fieldType ENUM('text', 'textarea', 'select', 'checkbox', 'multi_select', 'file_upload', 'user_select', 'multi_user_select') NOT NULL,
  fieldName VARCHAR(255) NOT NULL,
  label VARCHAR(255) NOT NULL,
  description TEXT,
  required BOOLEAN DEFAULT FALSE,
  options JSON, -- For select fields
  validation JSON, -- Validation rules
  displayOrder INT NOT NULL,
  FOREIGN KEY (formId) REFERENCES EventForm(id)
);
```

### API Endpoints
```
-- Land Steward form management
GET /api/land-steward/forms - List all forms
GET /api/land-steward/forms/templates - Form templates
POST /api/land-steward/forms/templates - Create form template
GET /api/land-steward/forms/[eventId] - Forms for specific event
POST /api/land-steward/forms/create - Create event form
PUT /api/land-steward/forms/[id] - Update form
DELETE /api/land-steward/forms/[id] - Delete form

-- Form submissions management
GET /api/land-steward/submissions - All form submissions
GET /api/land-steward/submissions/[formId] - Submissions for specific form
POST /api/land-steward/submissions/[id]/review - Review submission
GET /api/land-steward/export/submissions/[formId] - Export submissions as CSV

-- Captain form interaction
GET /api/captain/forms/available - Available forms for captain's ship
GET /api/captain/forms/[id] - Specific form details
POST /api/captain/forms/[id]/submit - Submit form response
GET /api/captain/forms/submissions - Captain's submitted forms
PUT /api/captain/forms/submissions/[id] - Update draft submission
```

### Form Builder Features
- Drag-and-drop form builder interface
- Multiple field types (text, dropdown, file upload, user selection)
- Form validation rules
- Event integration and assignment
- Template system for reusable forms
- Form preview functionality

### Form Management
- Captain form dashboard (available/submitted)
- Draft submission saving
- Form submission history
- Land Steward review interface
- Bulk submission management
- Advanced filtering and search

---

## Phase 5: Future Enhancements & Integrations

### Advanced Features
```sql
-- Ship products/marketplace
CREATE TABLE ShipProduct (
  id INT PRIMARY KEY AUTO_INCREMENT,
  shipId INT NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  price DECIMAL(10,2) NOT NULL,
  images JSON,
  status ENUM('draft', 'active', 'sold_out', 'discontinued') DEFAULT 'draft',
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (shipId) REFERENCES Ship(id)
);

-- Enhanced notifications
CREATE TABLE ShipNotification (
  id INT PRIMARY KEY AUTO_INCREMENT,
  shipId INT NOT NULL,
  userId INT NULL, -- NULL for ship-wide notifications
  type ENUM('member_joined', 'form_available', 'form_deadline', 'role_changed') NOT NULL,
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  read BOOLEAN DEFAULT FALSE,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (shipId) REFERENCES Ship(id),
  FOREIGN KEY (userId) REFERENCES User(id)
);

-- Volunteer integration
CREATE TABLE ShipVolunteerStats (
  id INT PRIMARY KEY AUTO_INCREMENT,
  shipId INT NOT NULL,
  userId INT NOT NULL,
  totalHours DECIMAL(10,2) DEFAULT 0,
  eventsParticipated INT DEFAULT 0,
  lastVolunteered DATETIME NULL,
  FOREIGN KEY (shipId) REFERENCES Ship(id),
  FOREIGN KEY (userId) REFERENCES User(id)
);
```

### Advanced Integration Features
- Ship-specific product marketplace
- Integration with existing notification system
- Volunteer hours tracking per ship member
- Advanced analytics and reporting
- Ship leaderboards and competitions
- Event participation tracking
- Member achievement system
- Ship newsletter/communication system

### Performance & Scalability
- Database indexing optimization
- Caching strategies for ship listings
- Image optimization for ship logos
- Background job processing for notifications
- API rate limiting for form submissions
- Search optimization with full-text indexing

---

## Implementation Notes

### Security Considerations
- Role-based access control validation
- Form submission rate limiting
- File upload security (logo/form attachments)
- Input sanitization for all user-generated content
- Audit logging for sensitive operations

### UI/UX Guidelines
- Consistent design with existing system
- Mobile-responsive design
- Accessibility compliance
- User-friendly error handling
- Progress indicators for multi-step processes
- Confirmation dialogs for destructive actions

### Testing Strategy
- Unit tests for all API endpoints
- Integration tests for form submission workflow
- E2E tests for critical user journeys
- Performance testing for ship listings
- Security testing for role permissions

### Migration Strategy
- Phased deployment approach
- Database migration scripts
- Feature flags for gradual rollout
- Rollback procedures for each phase
- User communication and training materials

This implementation plan provides a structured approach to building the Ships & Land Steward system while maintaining code quality and system reliability.