<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bank of Styx - Documentation Wiki</title>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            min-height: 100vh;
        }

        .sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            height: calc(100vh - 40px);
            position: sticky;
            top: 20px;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .main-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            height: calc(100vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
        }

        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo h1 {
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .logo p {
            color: #666;
            font-size: 0.9rem;
        }

        .search-box {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 14px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .search-box:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .nav-section {
            margin-bottom: 15px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            overflow: hidden;
        }

        .nav-section-header {
            color: #333;
            font-size: 1rem;
            margin: 0;
            padding: 12px 15px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-bottom: 1px solid #e1e5e9;
            cursor: pointer;
            user-select: none;
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-section-header:hover {
            background: linear-gradient(135deg, #e9ecef, #dee2e6);
        }

        .nav-section-header .toggle-icon {
            font-size: 0.8rem;
            transition: transform 0.3s ease;
        }

        .nav-section.collapsed .toggle-icon {
            transform: rotate(-90deg);
        }

        .nav-section-content {
            padding: 10px;
            transition: all 0.3s ease;
            max-height: 1000px;
            overflow: hidden;
        }

        .nav-section.collapsed .nav-section-content {
            max-height: 0;
            padding: 0 10px;
        }

        .nav-item {
            display: block;
            padding: 8px 12px;
            color: #555;
            text-decoration: none;
            border-radius: 8px;
            margin-bottom: 3px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .nav-item:hover {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: translateX(5px);
        }

        .nav-item.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .content-header {
            border-bottom: 3px solid #e1e5e9;
            padding-bottom: 15px;
            margin-bottom: 25px;
        }

        .content-header h1 {
            color: #333;
            font-size: 2.2rem;
            margin-bottom: 10px;
        }

        .content-header .breadcrumb {
            color: #666;
            font-size: 0.9rem;
        }

        .content-body {
            line-height: 1.8;
        }

        .content-body h2 {
            color: #333;
            font-size: 1.6rem;
            margin: 25px 0 15px 0;
            padding-bottom: 8px;
            border-bottom: 2px solid #e1e5e9;
        }

        .content-body h3 {
            color: #444;
            font-size: 1.3rem;
            margin: 20px 0 10px 0;
        }

        .content-body p {
            margin-bottom: 15px;
            color: #555;
        }

        .content-body ul, .content-body ol {
            margin: 15px 0 15px 25px;
        }

        .content-body li {
            margin-bottom: 8px;
            color: #555;
        }

        .content-body code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.9em;
            color: #e83e8c;
        }

        .content-body pre {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            overflow-x: auto;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .content-body pre code {
            background: none;
            padding: 0;
            color: #333;
        }

        .content-body blockquote {
            border-left: 4px solid #667eea;
            padding-left: 20px;
            margin: 20px 0;
            font-style: italic;
            color: #666;
        }

        .content-body a {
            color: #667eea;
            text-decoration: none;
            border-bottom: 1px solid transparent;
            transition: all 0.3s ease;
        }

        .content-body a:hover {
            border-bottom-color: #667eea;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #fee;
            border: 1px solid #fcc;
            border-radius: 8px;
            padding: 15px;
            color: #c33;
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                gap: 10px;
                padding: 10px;
            }
            
            .sidebar {
                position: static;
                order: 2;
            }
            
            .main-content {
                order: 1;
                padding: 20px;
            }
        }

        .file-tree {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.85em;
            overflow-x: auto;
        }

        .collapsible-section {
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            margin: 20px 0;
            overflow: hidden;
        }

        .collapsible-header {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 15px 20px;
            cursor: pointer;
            user-select: none;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
            border-bottom: 1px solid #e1e5e9;
        }

        .collapsible-header:hover {
            background: linear-gradient(135deg, #e9ecef, #dee2e6);
        }

        .collapsible-header h3 {
            margin: 0;
            color: #333;
            font-size: 1.2rem;
        }

        .collapsible-toggle {
            font-size: 1.2rem;
            transition: transform 0.3s ease;
            color: #667eea;
        }

        .collapsible-section.collapsed .collapsible-toggle {
            transform: rotate(-90deg);
        }

        .collapsible-content {
            padding: 20px;
            transition: all 0.3s ease;
            max-height: 2000px;
            overflow: hidden;
        }

        .collapsible-section.collapsed .collapsible-content {
            max-height: 0;
            padding: 0 20px;
        }

        .highlight {
            background: yellow;
            padding: 1px 3px;
            border-radius: 3px;
        }

        /* Custom scrollbar styles */
        .sidebar::-webkit-scrollbar,
        .main-content::-webkit-scrollbar {
            width: 8px;
        }

        .sidebar::-webkit-scrollbar-track,
        .main-content::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 4px;
        }

        .sidebar::-webkit-scrollbar-thumb,
        .main-content::-webkit-scrollbar-thumb {
            background: rgba(102, 126, 234, 0.5);
            border-radius: 4px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover,
        .main-content::-webkit-scrollbar-thumb:hover {
            background: rgba(102, 126, 234, 0.7);
        }
    </style>
</head>
<body>
    <div class="container">
        <aside class="sidebar">
            <div class="logo">
                <h1>🏛️ Bank of Styx</h1>
                <p>Documentation Wiki</p>
            </div>
            
            <input type="text" class="search-box" placeholder="Search documentation..." id="searchBox">
            
            <nav id="navigation">
                <!-- Navigation will be populated by JavaScript -->
            </nav>
        </aside>

        <main class="main-content">
            <div id="content">
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Loading documentation...</p>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Documentation structure and file mappings
        const docStructure = {
            'Overview': {
                'README.md': 'Project Overview',
                'PROJECT-STATUS.md': 'Project Status',
                'CLAUDE.md': 'Claude Documentation'
            },
            'Core Systems': {
                'docs/features/admin-dashboard-system.md': '🏛️ Administration System',
                'docs/features/banking-system.md': '🏦 Banking System',
                'docs/features/ship-management-system.md': '⚓ Ship Management',
                'docs/features/news-content-management-system.md': '📰 News & Content',
                'docs/features/volunteer-system.md': '🎯 Volunteer System',
                'docs/features/shopping-sales-system.md': '🛒 Shopping & Sales',
                'docs/features/real-time-notification-system.md': '🔔 Notifications'
            },
            'User Management': {
                'docs/features/authentication-system.md': '👤 Authentication',
                'docs/features/user-settings-profile-management-system.md': '⚙️ User Settings',
                'docs/features/support-system.md': '🎫 Support System'
            },
            'Developer Resources': {
                'docs/api/README.md': '🏗️ API Structure',
                'docs/components/ui-library.md': '🧩 Component Library',
                'docs/architecture/directory-structure.md': '🗂️ Directory Structure',
                'docs/development/setup-guide.md': '🔧 Development Guide',
                'docs/database/schema-documentation.md': '📜 Database Schema',
                'docs/technical/system-utilities.md': '🛠️ System Utilities',
                'docs/technical/upload-system.md': '📤 Upload System'
            },
            'Documentation': {
                'docs/README.md': '📚 Documentation System',
                'docs/TEMPLATE.md': '📝 Documentation Template',
                'docs/DOCUMENTATION-CHECKLIST.md': '✅ Documentation Checklist',
                'docs/STANDARDIZATION-GUIDE.md': '📋 Standardization Guide'
            },
            'Context & Implementation': {
                'context/README.md': '📖 Context Overview',
                'context/authentication.md': '🔐 Authentication Context',
                'context/banking-system.md': '💰 Banking Context',
                'context/event-volunteer-system.md': '🎪 Event & Volunteer Context',
                'context/news-content-management-system.md': '📰 News Context',
                'context/notification-system.md': '🔔 Notification Context',
                'context/product-shopping-system.md': '🛍️ Shopping Context',
                'context/support-ticket-system.md': '🎫 Support Context',
                'context/upload-system.md': '📤 Upload Context'
            },
            'Change Logs': {
                'CHANGELOG.md': '📝 Main Changelog',
                'CHANGELOG-volunteer-ship-tracking.md': '⚓ Volunteer Ship Tracking',
                'changelog/volunteer-checkin-system.md': '✅ Volunteer Check-in System'
            }
        };

        let currentFile = null;
        let searchIndex = [];

        // Initialize the application
        function init() {
            buildNavigation();
            setupSearch();
            loadDefaultContent();
        }

        // Build the navigation menu
        function buildNavigation() {
            const nav = document.getElementById('navigation');
            nav.innerHTML = '';

            Object.entries(docStructure).forEach(([section, files]) => {
                const sectionDiv = document.createElement('div');
                sectionDiv.className = 'nav-section';

                // Create collapsible header
                const sectionHeader = document.createElement('div');
                sectionHeader.className = 'nav-section-header';
                sectionHeader.innerHTML = `
                    <span>${section}</span>
                    <span class="toggle-icon">▼</span>
                `;

                // Create content container
                const sectionContent = document.createElement('div');
                sectionContent.className = 'nav-section-content';

                // Add click handler for collapsing
                sectionHeader.addEventListener('click', () => {
                    sectionDiv.classList.toggle('collapsed');
                });

                Object.entries(files).forEach(([filePath, displayName]) => {
                    const link = document.createElement('a');
                    link.href = '#';
                    link.className = 'nav-item';
                    link.textContent = displayName;
                    link.onclick = (e) => {
                        e.preventDefault();
                        loadFile(filePath, displayName);
                    };
                    sectionContent.appendChild(link);

                    // Build search index
                    searchIndex.push({
                        path: filePath,
                        name: displayName,
                        section: section,
                        element: link,
                        sectionElement: sectionDiv
                    });
                });

                sectionDiv.appendChild(sectionHeader);
                sectionDiv.appendChild(sectionContent);
                nav.appendChild(sectionDiv);
            });
        }

        // Setup search functionality
        function setupSearch() {
            const searchBox = document.getElementById('searchBox');

            searchBox.addEventListener('input', (e) => {
                const query = e.target.value.toLowerCase();
                filterNavigation(query);

                // Also highlight search terms in content if there's content loaded
                if (currentFile) {
                    highlightSearchInContent(query);
                }
            });

            // Add placeholder text with shortcut hint
            searchBox.placeholder = 'Search documentation... (Ctrl+K)';
        }

        // Highlight search terms in the current content
        function highlightSearchInContent(query) {
            const contentBody = document.querySelector('.content-body');
            if (!contentBody || !query) return;

            // Remove existing highlights
            contentBody.innerHTML = contentBody.innerHTML.replace(/<span class="highlight">(.*?)<\/span>/gi, '$1');

            if (query.length > 2) {
                // Add new highlights
                const regex = new RegExp(`(${escapeRegex(query)})`, 'gi');
                contentBody.innerHTML = contentBody.innerHTML.replace(regex, '<span class="highlight">$1</span>');
            }
        }

        // Escape special regex characters
        function escapeRegex(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        }

        // Filter navigation based on search query
        function filterNavigation(query) {
            const sectionsWithMatches = new Set();

            searchIndex.forEach(item => {
                const matches = item.name.toLowerCase().includes(query) ||
                               item.section.toLowerCase().includes(query);
                item.element.style.display = matches ? 'block' : 'none';

                if (matches) {
                    sectionsWithMatches.add(item.sectionElement);
                }
            });

            // Show/hide sections and expand sections with matches
            document.querySelectorAll('.nav-section').forEach(section => {
                const hasMatches = sectionsWithMatches.has(section);
                section.style.display = hasMatches ? 'block' : 'none';

                // If searching and section has matches, expand it
                if (query && hasMatches) {
                    section.classList.remove('collapsed');
                }
            });

            // If no search query, show all sections
            if (!query) {
                document.querySelectorAll('.nav-section').forEach(section => {
                    section.style.display = 'block';
                });
            }
        }

        // Load default content (README.md)
        function loadDefaultContent() {
            loadFile('README.md', 'Project Overview');
        }

        // Load and display a documentation file
        async function loadFile(filePath, displayName) {
            const content = document.getElementById('content');

            // Update active navigation
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            const activeItem = searchIndex.find(item => item.path === filePath);
            if (activeItem) {
                activeItem.element.classList.add('active');
            }

            // Show loading state
            content.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Loading ${displayName}...</p>
                </div>
            `;

            try {
                const fileContent = await loadFileContent(filePath);
                displayContent(fileContent, displayName, filePath);
                currentFile = filePath;
            } catch (error) {
                content.innerHTML = `
                    <div class="error">
                        <h2>Error Loading File</h2>
                        <p>Could not load ${filePath}: ${error.message}</p>
                        <p>Make sure the file exists and is accessible.</p>
                    </div>
                `;
            }
        }

        // Load actual file content from the filesystem
        async function loadFileContent(filePath) {
            try {
                const response = await fetch(filePath);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const content = await response.text();
                return content;
            } catch (error) {
                // If direct fetch fails (CORS issues), try alternative approaches
                throw new Error(`Failed to load file: ${error.message}`);
            }
        }



        // Display content in the main area
        function displayContent(content, title, filePath) {
            const contentDiv = document.getElementById('content');

            // Create breadcrumb
            const pathParts = filePath.split('/');
            const breadcrumb = pathParts.length > 1 ?
                pathParts.slice(0, -1).join(' / ') + ' / ' + title :
                title;

            // Convert markdown-like content to HTML
            let htmlContent = parseMarkdown(content);

            // Make large content blocks collapsible
            htmlContent = makeContentCollapsible(htmlContent);

            contentDiv.innerHTML = `
                <div class="content-header">
                    <h1>${title}</h1>
                    <div class="breadcrumb">${breadcrumb}</div>
                </div>
                <div class="content-body">
                    ${htmlContent}
                </div>
            `;

            // Add click handlers for internal links and collapsible sections
            addInternalLinkHandlers();
            addCollapsibleHandlers();
        }

        // Make large content blocks collapsible
        function makeContentCollapsible(html) {
            // Make directory structures collapsible
            html = html.replace(
                /(<h[2-6]>.*(?:Directory|Structure|Tree|Organization|Architecture).*<\/h[2-6]>)([\s\S]*?)(?=<h[1-6]|$)/gi,
                (match, header, content) => {
                    if (content.length > 500) {
                        const headerId = 'collapse-' + Math.random().toString(36).substr(2, 9);
                        return `
                            <div class="collapsible-section" id="${headerId}">
                                <div class="collapsible-header">
                                    ${header}
                                    <span class="collapsible-toggle">▼</span>
                                </div>
                                <div class="collapsible-content">
                                    ${content}
                                </div>
                            </div>
                        `;
                    }
                    return match;
                }
            );

            // Make large code blocks collapsible
            html = html.replace(
                /<pre><code[^>]*>([\s\S]*?)<\/code><\/pre>/g,
                (match, code) => {
                    if (code.length > 1000) {
                        const codeId = 'code-' + Math.random().toString(36).substr(2, 9);
                        return `
                            <div class="collapsible-section" id="${codeId}">
                                <div class="collapsible-header">
                                    <h3>📄 Code Block</h3>
                                    <span class="collapsible-toggle">▼</span>
                                </div>
                                <div class="collapsible-content">
                                    ${match}
                                </div>
                            </div>
                        `;
                    }
                    return match;
                }
            );

            return html;
        }

        // Add handlers for collapsible content sections
        function addCollapsibleHandlers() {
            document.querySelectorAll('.collapsible-header').forEach(header => {
                header.addEventListener('click', () => {
                    const section = header.parentElement;
                    section.classList.toggle('collapsed');
                });
            });
        }

        // Parse markdown content using marked.js library
        function parseMarkdown(content) {
            if (typeof marked !== 'undefined') {
                // Configure marked for better rendering
                marked.setOptions({
                    highlight: function(code, lang) {
                        // Basic syntax highlighting for common languages
                        return `<code class="language-${lang || 'text'}">${escapeHtml(code)}</code>`;
                    },
                    breaks: true,
                    gfm: true
                });
                return marked.parse(content);
            } else {
                // Fallback basic markdown parser if marked.js fails to load
                return content
                    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                    .replace(/```[\s\S]*?```/g, '<pre><code>$&</code></pre>')
                    .replace(/`([^`]+)`/g, '<code>$1</code>')
                    .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*([^*]+)\*/g, '<em>$1</em>')
                    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
                    .replace(/^\- (.+$)/gim, '<li>$1</li>')
                    .replace(/\n\n/g, '</p><p>')
                    .replace(/^(.+)$/gm, '<p>$1</p>');
            }
        }

        // Escape HTML characters
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Add handlers for internal documentation links
        function addInternalLinkHandlers() {
            const links = document.querySelectorAll('.content-body a');
            links.forEach(link => {
                const href = link.getAttribute('href');
                if (href && href.endsWith('.md')) {
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        // Find the file in our structure
                        for (const [section, files] of Object.entries(docStructure)) {
                            for (const [filePath, displayName] of Object.entries(files)) {
                                if (filePath.endsWith(href) || filePath === href) {
                                    loadFile(filePath, displayName);
                                    return;
                                }
                            }
                        }
                        // If not found, show error
                        alert(`Documentation file not found: ${href}`);
                    });
                }
            });
        }

        // Highlight search terms in content
        function highlightSearchTerms(content, searchTerm) {
            if (!searchTerm) return content;

            const regex = new RegExp(`(${searchTerm})`, 'gi');
            return content.replace(regex, '<span class="highlight">$1</span>');
        }

        // Initialize the application when DOM is loaded
        document.addEventListener('DOMContentLoaded', init);

        // Add keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K to focus search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchBox = document.getElementById('searchBox');
                searchBox.focus();
                searchBox.select();
            }

            // Escape to clear search or unfocus
            if (e.key === 'Escape') {
                const searchBox = document.getElementById('searchBox');
                if (document.activeElement === searchBox) {
                    if (searchBox.value) {
                        searchBox.value = '';
                        filterNavigation('');
                        highlightSearchInContent('');
                    } else {
                        searchBox.blur();
                    }
                }
            }

            // Ctrl/Cmd + / to toggle all navigation sections
            if ((e.ctrlKey || e.metaKey) && e.key === '/') {
                e.preventDefault();
                toggleAllNavigationSections();
            }
        });

        // Toggle all navigation sections
        function toggleAllNavigationSections() {
            const sections = document.querySelectorAll('.nav-section');
            const allCollapsed = Array.from(sections).every(section =>
                section.classList.contains('collapsed')
            );

            sections.forEach(section => {
                if (allCollapsed) {
                    section.classList.remove('collapsed');
                } else {
                    section.classList.add('collapsed');
                }
            });
        }
    </script>
</body>
</html>
