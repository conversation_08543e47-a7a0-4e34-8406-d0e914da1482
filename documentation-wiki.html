<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bank of Styx - Documentation Wiki</title>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            min-height: 100vh;
        }

        .sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            height: fit-content;
            position: sticky;
            top: 20px;
        }

        .main-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            min-height: calc(100vh - 40px);
        }

        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo h1 {
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .logo p {
            color: #666;
            font-size: 0.9rem;
        }

        .search-box {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 14px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .search-box:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .nav-section {
            margin-bottom: 25px;
        }

        .nav-section h3 {
            color: #333;
            font-size: 1rem;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 2px solid #e1e5e9;
        }

        .nav-item {
            display: block;
            padding: 8px 12px;
            color: #555;
            text-decoration: none;
            border-radius: 8px;
            margin-bottom: 3px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .nav-item:hover {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: translateX(5px);
        }

        .nav-item.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .content-header {
            border-bottom: 3px solid #e1e5e9;
            padding-bottom: 15px;
            margin-bottom: 25px;
        }

        .content-header h1 {
            color: #333;
            font-size: 2.2rem;
            margin-bottom: 10px;
        }

        .content-header .breadcrumb {
            color: #666;
            font-size: 0.9rem;
        }

        .content-body {
            line-height: 1.8;
        }

        .content-body h2 {
            color: #333;
            font-size: 1.6rem;
            margin: 25px 0 15px 0;
            padding-bottom: 8px;
            border-bottom: 2px solid #e1e5e9;
        }

        .content-body h3 {
            color: #444;
            font-size: 1.3rem;
            margin: 20px 0 10px 0;
        }

        .content-body p {
            margin-bottom: 15px;
            color: #555;
        }

        .content-body ul, .content-body ol {
            margin: 15px 0 15px 25px;
        }

        .content-body li {
            margin-bottom: 8px;
            color: #555;
        }

        .content-body code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.9em;
            color: #e83e8c;
        }

        .content-body pre {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            overflow-x: auto;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .content-body pre code {
            background: none;
            padding: 0;
            color: #333;
        }

        .content-body blockquote {
            border-left: 4px solid #667eea;
            padding-left: 20px;
            margin: 20px 0;
            font-style: italic;
            color: #666;
        }

        .content-body a {
            color: #667eea;
            text-decoration: none;
            border-bottom: 1px solid transparent;
            transition: all 0.3s ease;
        }

        .content-body a:hover {
            border-bottom-color: #667eea;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #fee;
            border: 1px solid #fcc;
            border-radius: 8px;
            padding: 15px;
            color: #c33;
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                gap: 10px;
                padding: 10px;
            }
            
            .sidebar {
                position: static;
                order: 2;
            }
            
            .main-content {
                order: 1;
                padding: 20px;
            }
        }

        .file-tree {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.85em;
            overflow-x: auto;
        }

        .highlight {
            background: yellow;
            padding: 1px 3px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <aside class="sidebar">
            <div class="logo">
                <h1>🏛️ Bank of Styx</h1>
                <p>Documentation Wiki</p>
            </div>
            
            <input type="text" class="search-box" placeholder="Search documentation..." id="searchBox">
            
            <nav id="navigation">
                <!-- Navigation will be populated by JavaScript -->
            </nav>
        </aside>

        <main class="main-content">
            <div id="content">
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Loading documentation...</p>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Documentation structure and file mappings
        const docStructure = {
            'Overview': {
                'README.md': 'Project Overview',
                'PROJECT-STATUS.md': 'Project Status',
                'CLAUDE.md': 'Claude Documentation'
            },
            'Core Systems': {
                'docs/features/admin-dashboard-system.md': '🏛️ Administration System',
                'docs/features/banking-system.md': '🏦 Banking System',
                'docs/features/ship-management-system.md': '⚓ Ship Management',
                'docs/features/news-content-management-system.md': '📰 News & Content',
                'docs/features/volunteer-system.md': '🎯 Volunteer System',
                'docs/features/shopping-sales-system.md': '🛒 Shopping & Sales',
                'docs/features/real-time-notification-system.md': '🔔 Notifications'
            },
            'User Management': {
                'docs/features/authentication-system.md': '👤 Authentication',
                'docs/features/user-settings-profile-management-system.md': '⚙️ User Settings',
                'docs/features/support-system.md': '🎫 Support System'
            },
            'Developer Resources': {
                'docs/api/README.md': '🏗️ API Structure',
                'docs/components/ui-library.md': '🧩 Component Library',
                'docs/architecture/directory-structure.md': '🗂️ Directory Structure',
                'docs/development/setup-guide.md': '🔧 Development Guide',
                'docs/database/schema-documentation.md': '📜 Database Schema',
                'docs/technical/system-utilities.md': '🛠️ System Utilities',
                'docs/technical/upload-system.md': '📤 Upload System'
            },
            'Documentation': {
                'docs/README.md': '📚 Documentation System',
                'docs/TEMPLATE.md': '📝 Documentation Template',
                'docs/DOCUMENTATION-CHECKLIST.md': '✅ Documentation Checklist',
                'docs/STANDARDIZATION-GUIDE.md': '📋 Standardization Guide'
            },
            'Context & Implementation': {
                'context/README.md': '📖 Context Overview',
                'context/authentication.md': '🔐 Authentication Context',
                'context/banking-system.md': '💰 Banking Context',
                'context/event-volunteer-system.md': '🎪 Event & Volunteer Context',
                'context/news-content-management-system.md': '📰 News Context',
                'context/notification-system.md': '🔔 Notification Context',
                'context/product-shopping-system.md': '🛍️ Shopping Context',
                'context/support-ticket-system.md': '🎫 Support Context',
                'context/upload-system.md': '📤 Upload Context'
            },
            'Change Logs': {
                'CHANGELOG.md': '📝 Main Changelog',
                'CHANGELOG-volunteer-ship-tracking.md': '⚓ Volunteer Ship Tracking',
                'changelog/volunteer-checkin-system.md': '✅ Volunteer Check-in System'
            }
        };

        let currentFile = null;
        let searchIndex = [];

        // Initialize the application
        function init() {
            buildNavigation();
            setupSearch();
            loadDefaultContent();
        }

        // Build the navigation menu
        function buildNavigation() {
            const nav = document.getElementById('navigation');
            nav.innerHTML = '';

            Object.entries(docStructure).forEach(([section, files]) => {
                const sectionDiv = document.createElement('div');
                sectionDiv.className = 'nav-section';

                const sectionTitle = document.createElement('h3');
                sectionTitle.textContent = section;
                sectionDiv.appendChild(sectionTitle);

                Object.entries(files).forEach(([filePath, displayName]) => {
                    const link = document.createElement('a');
                    link.href = '#';
                    link.className = 'nav-item';
                    link.textContent = displayName;
                    link.onclick = (e) => {
                        e.preventDefault();
                        loadFile(filePath, displayName);
                    };
                    sectionDiv.appendChild(link);

                    // Build search index
                    searchIndex.push({
                        path: filePath,
                        name: displayName,
                        section: section,
                        element: link
                    });
                });

                nav.appendChild(sectionDiv);
            });
        }

        // Setup search functionality
        function setupSearch() {
            const searchBox = document.getElementById('searchBox');
            searchBox.addEventListener('input', (e) => {
                const query = e.target.value.toLowerCase();
                filterNavigation(query);
            });
        }

        // Filter navigation based on search query
        function filterNavigation(query) {
            searchIndex.forEach(item => {
                const matches = item.name.toLowerCase().includes(query) ||
                               item.section.toLowerCase().includes(query);
                item.element.style.display = matches ? 'block' : 'none';

                // Also hide/show section headers if no items match
                const section = item.element.parentNode;
                const visibleItems = Array.from(section.querySelectorAll('.nav-item'))
                    .filter(el => el.style.display !== 'none');
                section.style.display = visibleItems.length > 0 ? 'block' : 'none';
            });
        }

        // Load default content (README.md)
        function loadDefaultContent() {
            loadFile('README.md', 'Project Overview');
        }

        // Load and display a documentation file
        async function loadFile(filePath, displayName) {
            const content = document.getElementById('content');

            // Update active navigation
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            const activeItem = searchIndex.find(item => item.path === filePath);
            if (activeItem) {
                activeItem.element.classList.add('active');
            }

            // Show loading state
            content.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Loading ${displayName}...</p>
                </div>
            `;

            try {
                const fileContent = await loadFileContent(filePath);
                displayContent(fileContent, displayName, filePath);
                currentFile = filePath;
            } catch (error) {
                content.innerHTML = `
                    <div class="error">
                        <h2>Error Loading File</h2>
                        <p>Could not load ${filePath}: ${error.message}</p>
                        <p>Make sure the file exists and is accessible.</p>
                    </div>
                `;
            }
        }

        // Load actual file content from the filesystem
        async function loadFileContent(filePath) {
            try {
                const response = await fetch(filePath);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const content = await response.text();
                return content;
            } catch (error) {
                // If direct fetch fails (CORS issues), try alternative approaches
                throw new Error(`Failed to load file: ${error.message}`);
            }
        }



        // Display content in the main area
        function displayContent(content, title, filePath) {
            const contentDiv = document.getElementById('content');

            // Create breadcrumb
            const pathParts = filePath.split('/');
            const breadcrumb = pathParts.length > 1 ?
                pathParts.slice(0, -1).join(' / ') + ' / ' + title :
                title;

            // Convert markdown-like content to HTML (basic implementation)
            const htmlContent = parseMarkdown(content);

            contentDiv.innerHTML = `
                <div class="content-header">
                    <h1>${title}</h1>
                    <div class="breadcrumb">${breadcrumb}</div>
                </div>
                <div class="content-body">
                    ${htmlContent}
                </div>
            `;

            // Add click handlers for internal links
            addInternalLinkHandlers();
        }

        // Parse markdown content using marked.js library
        function parseMarkdown(content) {
            if (typeof marked !== 'undefined') {
                // Configure marked for better rendering
                marked.setOptions({
                    highlight: function(code, lang) {
                        // Basic syntax highlighting for common languages
                        return `<code class="language-${lang || 'text'}">${escapeHtml(code)}</code>`;
                    },
                    breaks: true,
                    gfm: true
                });
                return marked.parse(content);
            } else {
                // Fallback basic markdown parser if marked.js fails to load
                return content
                    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                    .replace(/```[\s\S]*?```/g, '<pre><code>$&</code></pre>')
                    .replace(/`([^`]+)`/g, '<code>$1</code>')
                    .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*([^*]+)\*/g, '<em>$1</em>')
                    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
                    .replace(/^\- (.+$)/gim, '<li>$1</li>')
                    .replace(/\n\n/g, '</p><p>')
                    .replace(/^(.+)$/gm, '<p>$1</p>');
            }
        }

        // Escape HTML characters
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Add handlers for internal documentation links
        function addInternalLinkHandlers() {
            const links = document.querySelectorAll('.content-body a');
            links.forEach(link => {
                const href = link.getAttribute('href');
                if (href && href.endsWith('.md')) {
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        // Find the file in our structure
                        for (const [section, files] of Object.entries(docStructure)) {
                            for (const [filePath, displayName] of Object.entries(files)) {
                                if (filePath.endsWith(href) || filePath === href) {
                                    loadFile(filePath, displayName);
                                    return;
                                }
                            }
                        }
                        // If not found, show error
                        alert(`Documentation file not found: ${href}`);
                    });
                }
            });
        }

        // Highlight search terms in content
        function highlightSearchTerms(content, searchTerm) {
            if (!searchTerm) return content;

            const regex = new RegExp(`(${searchTerm})`, 'gi');
            return content.replace(regex, '<span class="highlight">$1</span>');
        }

        // Initialize the application when DOM is loaded
        document.addEventListener('DOMContentLoaded', init);

        // Add keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K to focus search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                document.getElementById('searchBox').focus();
            }

            // Escape to clear search
            if (e.key === 'Escape') {
                const searchBox = document.getElementById('searchBox');
                if (searchBox.value) {
                    searchBox.value = '';
                    filterNavigation('');
                }
            }
        });
    </script>
</body>
</html>
