# How to Save Conversations

## Quick Commands

To save the current conversation before clearing context, use any of these phrases:

- **"Save this conversation and clear context"**
- **"Save conversation summary"**
- **"Generate conversation bulletin"**
- **"Export session summary"**

## What Gets Saved

<PERSON> will automatically generate a bulletin format summary including:

- ✅ **Key Accomplishments** - Features implemented, bugs fixed
- 📁 **Files Modified/Created** - Complete code change list  
- 🔧 **Technical Decisions** - Architecture choices made
- ⚠️ **Outstanding Issues** - Unresolved problems
- 📋 **Next Steps** - Recommended follow-up actions
- 🧪 **Testing Status** - What was verified

## File Location

Summaries are saved to: `conversation-history/session-[date]-[timestamp].md`

## Workspace Memory

The system maintains context between sessions via `.claude-memory.json` which tracks:
- Current session progress
- Ongoing work items
- Files being modified
- Technical decisions made

This enables <PERSON> to provide continuity even after context clears.