#!/usr/bin/env python3
"""
Simple HTTP server to serve documentation files for the wiki.
This allows the documentation wiki to fetch markdown files without CORS issues.
"""

import http.server
import socketserver
import os
import sys
import json
import re
from urllib.parse import unquote, urlparse, parse_qs
import mimetypes

class DocumentationHandler(http.server.SimpleHTTPRequestHandler):
    # Define the documentation structure for searching as a class variable
    doc_files = [
        'README.md', 'PROJECT-STATUS.md', 'CLAUDE.md', 'Continuation.md',
        'CHANGELOG.md', 'CHANGELOG-volunteer-ship-tracking.md',
        'ship-land-steward-implementation.md',
        'Revised Form Builder enhancement.md',
        'docs/README.md', 'docs/TEMPLATE.md', 'docs/DOCUMENTATION-CHECKLIST.md',
        'docs/STANDARDIZATION-GUIDE.md',
        'docs/api/README.md',
        'docs/architecture/directory-structure.md',
        'docs/components/ui-library.md',
        'docs/database/schema-documentation.md',
        'docs/development/setup-guide.md',
        'docs/development/performance-optimization-guide.md',
        'docs/features/README.md',
        'docs/features/admin-dashboard-system.md',
        'docs/features/authentication-system.md',
        'docs/features/banking-system.md',
        'docs/features/news-content-management-system.md',
        'docs/features/real-time-notification-system.md',
        'docs/features/ship-management-system.md',
        'docs/features/shopping-sales-system.md',
        'docs/features/support-system.md',
        'docs/features/user-settings-profile-management-system.md',
        'docs/features/volunteer-system.md',
        'docs/technical/system-utilities.md',
        'docs/technical/upload-system.md',
        'context/README.md',
        'context/authentication.md',
        'context/banking-system.md',
        'context/color-theme-ui-elements.md',
        'context/event-volunteer-system.md',
        'context/news-content-management-system.md',
        'context/notification-system.md',
        'context/product-shopping-system.md',
        'context/real-time-sse.md',
        'context/support-ticket-system.md',
        'context/upload-system.md',
        'context/Event-Based-Capacity-System-Changes.md',
        'changelog/volunteer-checkin-system.md',
        'conversation-history/README.md'
    ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def end_headers(self):
        # Add CORS headers to allow the wiki to fetch files
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        # Handle preflight requests
        self.send_response(200)
        self.end_headers()
    
    def do_GET(self):
        # Parse the URL
        parsed_url = urlparse(self.path)
        path = unquote(parsed_url.path)
        query_params = parse_qs(parsed_url.query)

        # Remove leading slash
        if path.startswith('/'):
            path = path[1:]

        # Handle search API endpoint
        if path == 'api/search':
            self.handle_search_api(query_params)
            return

        # If no path specified, serve the wiki
        if not path or path == '/':
            path = 'documentation-wiki.html'

        # Security check - prevent directory traversal
        if '..' in path or path.startswith('/'):
            self.send_error(403, "Forbidden")
            return

        # Check if file exists
        if not os.path.exists(path):
            self.send_error(404, f"File not found: {path}")
            return

        # Serve the file
        try:
            with open(path, 'rb') as f:
                content = f.read()

            # Determine content type
            content_type, _ = mimetypes.guess_type(path)
            if content_type is None:
                if path.endswith('.md'):
                    content_type = 'text/markdown'
                else:
                    content_type = 'text/plain'

            self.send_response(200)
            self.send_header('Content-Type', content_type)
            self.send_header('Content-Length', str(len(content)))
            self.end_headers()
            self.wfile.write(content)

        except Exception as e:
            self.send_error(500, f"Internal server error: {str(e)}")

    def handle_search_api(self, query_params):
        """Handle search API requests"""
        try:
            # Get search query
            query = query_params.get('q', [''])[0].strip()
            if not query:
                self.send_json_response({'results': [], 'message': 'No query provided'})
                return

            print(f"Searching for: '{query}'")

            # Perform search
            results = self.search_files(query)

            print(f"Found {len(results)} results")

            self.send_json_response({
                'results': results,
                'query': query,
                'total_matches': len(results)
            })

        except Exception as e:
            print(f"Search API error: {e}")
            import traceback
            traceback.print_exc()
            self.send_json_response({'error': str(e)}, status=500)

    def search_files(self, query):
        """Search through all documentation files"""
        results = []
        query_lower = query.lower()

        print(f"Searching through {len(self.doc_files)} files...")

        for file_path in self.doc_files:
            if not os.path.exists(file_path):
                print(f"File not found: {file_path}")
                continue

            try:
                print(f"Searching in: {file_path}")
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()

                # Search for matches
                matches = self.find_matches_in_content(content, query_lower, file_path)
                if matches:
                    print(f"Found {len(matches)} matches in {file_path}")
                    results.extend(matches)

            except Exception as e:
                print(f"Error reading {file_path}: {e}")
                continue

        # Sort results by relevance (number of matches, then by file importance)
        results.sort(key=lambda x: (-x['match_count'], x['file_path']))

        return results

    def find_matches_in_content(self, content, query, file_path):
        """Find matches within file content and return context"""
        matches = []
        lines = content.split('\n')

        for line_num, line in enumerate(lines, 1):
            line_lower = line.lower()
            if query in line_lower:
                # Get context (2 lines before and after)
                start_line = max(0, line_num - 3)
                end_line = min(len(lines), line_num + 2)
                context_lines = lines[start_line:end_line]

                # Highlight the search term in the matching line
                highlighted_line = self.highlight_search_term(line, query)

                matches.append({
                    'file_path': file_path,
                    'line_number': line_num,
                    'line_content': highlighted_line,
                    'context': context_lines,
                    'match_count': line_lower.count(query)
                })

        return matches

    def highlight_search_term(self, text, query):
        """Highlight search term in text (case insensitive)"""
        # Use regex for case-insensitive replacement
        pattern = re.compile(re.escape(query), re.IGNORECASE)
        return pattern.sub(f'**{query}**', text)

    def send_json_response(self, data, status=200):
        """Send JSON response"""
        json_data = json.dumps(data, indent=2).encode('utf-8')

        self.send_response(status)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Content-Length', str(len(json_data)))
        self.end_headers()
        self.wfile.write(json_data)
    
    def log_message(self, format, *args):
        # Custom logging to show which files are being served
        message = format % args
        print(f"[{self.address_string()}] {message}")

def main():
    PORT = 8001
    
    # Check if port is already in use
    try:
        with socketserver.TCPServer(("", PORT), DocumentationHandler) as httpd:
            print(f"🚀 Documentation server starting...")
            print(f"📚 Serving documentation from: {os.getcwd()}")
            print(f"🌐 Open your browser to: http://localhost:{PORT}")
            print(f"📖 Wiki available at: http://localhost:{PORT}/documentation-wiki.html")
            print(f"⏹️  Press Ctrl+C to stop the server")
            print("-" * 60)
            
            try:
                httpd.serve_forever()
            except KeyboardInterrupt:
                print("\n🛑 Server stopped by user")
                
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ Port {PORT} is already in use.")
            print(f"💡 Try stopping other servers or use a different port.")
            sys.exit(1)
        else:
            raise

if __name__ == "__main__":
    main()
