#!/usr/bin/env python3
"""
Simple HTTP server to serve documentation files for the wiki.
This allows the documentation wiki to fetch markdown files without CORS issues.
"""

import http.server
import socketserver
import os
import sys
from urllib.parse import unquote
import mimetypes

class DocumentationHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def end_headers(self):
        # Add CORS headers to allow the wiki to fetch files
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        # Handle preflight requests
        self.send_response(200)
        self.end_headers()
    
    def do_GET(self):
        # Decode the URL path
        path = unquote(self.path)
        
        # Remove leading slash
        if path.startswith('/'):
            path = path[1:]
        
        # If no path specified, serve the wiki
        if not path or path == '/':
            path = 'documentation-wiki.html'
        
        # Security check - prevent directory traversal
        if '..' in path or path.startswith('/'):
            self.send_error(403, "Forbidden")
            return
        
        # Check if file exists
        if not os.path.exists(path):
            self.send_error(404, f"File not found: {path}")
            return
        
        # Serve the file
        try:
            with open(path, 'rb') as f:
                content = f.read()
            
            # Determine content type
            content_type, _ = mimetypes.guess_type(path)
            if content_type is None:
                if path.endswith('.md'):
                    content_type = 'text/markdown'
                else:
                    content_type = 'text/plain'
            
            self.send_response(200)
            self.send_header('Content-Type', content_type)
            self.send_header('Content-Length', str(len(content)))
            self.end_headers()
            self.wfile.write(content)
            
        except Exception as e:
            self.send_error(500, f"Internal server error: {str(e)}")
    
    def log_message(self, format, *args):
        # Custom logging to show which files are being served
        message = format % args
        print(f"[{self.address_string()}] {message}")

def main():
    PORT = 8000
    
    # Check if port is already in use
    try:
        with socketserver.TCPServer(("", PORT), DocumentationHandler) as httpd:
            print(f"🚀 Documentation server starting...")
            print(f"📚 Serving documentation from: {os.getcwd()}")
            print(f"🌐 Open your browser to: http://localhost:{PORT}")
            print(f"📖 Wiki available at: http://localhost:{PORT}/documentation-wiki.html")
            print(f"⏹️  Press Ctrl+C to stop the server")
            print("-" * 60)
            
            try:
                httpd.serve_forever()
            except KeyboardInterrupt:
                print("\n🛑 Server stopped by user")
                
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ Port {PORT} is already in use.")
            print(f"💡 Try stopping other servers or use a different port.")
            sys.exit(1)
        else:
            raise

if __name__ == "__main__":
    main()
