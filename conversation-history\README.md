# Conversation History

This folder contains auto-generated conversation summaries from Claude Code sessions.

## Format

Each conversation summary includes:
- **Key Accomplishments**: Major features implemented or problems solved
- **Files Modified/Created**: Complete list of code changes
- **Technical Decisions**: Architecture choices and implementation details
- **Outstanding Issues**: Unresolved problems or bugs
- **Next Steps**: Recommended follow-up actions
- **Testing Status**: What was tested and verified

## Usage

When you want to clear context while preserving the conversation:

1. Say: **"Save this conversation and clear context"**
2. <PERSON> will generate a summary bulletin
3. Save it to this folder with timestamp
4. You can then safely clear context

## File Naming Convention

```
session-YYYY-MM-DD-timestamp.md
```

Example: `session-2025-07-30-1722340800.md`

## Memory System

The workspace uses `.claude-memory.json` to track:
- Current session progress
- Key accomplishments
- Files being worked on
- Outstanding issues
- Next steps

This enables automatic conversation summarization and continuity between sessions.