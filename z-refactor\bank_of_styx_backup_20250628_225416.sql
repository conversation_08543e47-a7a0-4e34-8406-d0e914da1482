﻿-- MySQL dump 10.13  Distrib 8.0.42, for Win64 (x86_64)
--
-- Host: localhost    Database: bank_of_styx
-- ------------------------------------------------------
-- Server version	8.0.42

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `_prisma_migrations`
--

DROP TABLE IF EXISTS `_prisma_migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `_prisma_migrations` (
  `id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `checksum` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `finished_at` datetime(3) DEFAULT NULL,
  `migration_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `logs` text COLLATE utf8mb4_unicode_ci,
  `rolled_back_at` datetime(3) DEFAULT NULL,
  `started_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `applied_steps_count` int unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `_prisma_migrations`
--

LOCK TABLES `_prisma_migrations` WRITE;
/*!40000 ALTER TABLE `_prisma_migrations` DISABLE KEYS */;
INSERT INTO `_prisma_migrations` VALUES ('018c8d39-797d-437b-9ec1-f9b7bbc93a99','24aad540dcb32ec3e4c3bfcea197b8d4a87378fc2722529d39cdc56d9c5917fb','2025-05-30 10:29:19.031','20250508014005_add_notification_sync_timestamp',NULL,NULL,'2025-05-30 10:29:18.975',1),('123ab8b6-06cb-4f29-b584-c90ecef4e292','357deb3016e938ba75550fb106ab65e0a83cb97aa323abf20c4de7824e8f7c83','2025-05-30 10:29:16.670','20250427154050_news_models',NULL,NULL,'2025-05-30 10:29:16.517',1),('1448ae9a-e80c-492a-ac91-8f3ee864ba5d','348d2908a4f42a98786695663076c5e52cbef8835fd4a31ff8c55ad33cc9de87','2025-05-30 10:29:18.080','20250428171432_add_notification_model',NULL,NULL,'2025-05-30 10:29:17.242',1),('1c2f3ff0-59c0-465d-bed1-2a7694012195','b8541e4f083db4f2ff53b68d7c748aa824af1780f30a109a1e1cc491cefa1faa','2025-05-30 10:29:18.661','20250503153135_add_verification_tables',NULL,NULL,'2025-05-30 10:29:18.505',1),('27468425-3fe1-4142-9c3d-1fcf44f3d690','1eca50fc10e7c6639edc63fa146e79d866f1d1433ec676f1c90bfd8e94743b01','2025-05-30 10:29:18.973','20250504161232_add_support_ticket_system',NULL,NULL,'2025-05-30 10:29:18.663',1),('2dceccb1-7789-4765-811d-f8cad465a9b8','a630042b941f982d9983de10425d8e08811e1f7cc7ceb2eccb54a2428784240a','2025-05-30 10:29:18.415','20250502135814_enhance_notification_system',NULL,NULL,'2025-05-30 10:29:18.403',1),('33beecc2-b2b1-4d2b-948c-033578782ff9','043e195cc5caef33835a57117ad96d6d46609ceca9f32553da58986ccd4ea2f5','2025-05-30 10:29:20.151','20250515110413_add_volunteer_coordinator_and_lead_manager',NULL,NULL,'2025-05-30 10:29:19.913',1),('35097635-8686-464b-a107-ea009fa824fa','83c45e32bc2afc8dfab31a79656d03c5588cac329963fdeb0968ec27b426fc3c','2025-05-30 10:29:19.097','20250513001549_add_image_tracking',NULL,NULL,'2025-05-30 10:29:19.034',1),('615231c2-12fd-4d62-9591-d24d39ac2490','541095deed26c37b51a58c2339566ccbd8479edec541770f8f6a5f83873ae3c0','2025-05-30 10:29:16.514','20250426214838_init',NULL,NULL,'2025-05-30 10:29:16.480',1),('63fde859-4fb5-4775-beaa-0f5c60ba11ba','d4c6793428f430f4e9bd78f24c07dd39cd04cf8b4b366674545b421ab2e7073b','2025-05-30 10:29:19.911','20250514224427_add_volunteer_models',NULL,NULL,'2025-05-30 10:29:19.278',1),('71ca0935-f444-4587-a32b-270dc4ec7182','77848d9a0ca01349cd6a031ca60950ab17ad990cedefdf0fcded9b16b443d1a9','2025-05-30 10:29:18.135','20250430161805_add_user_status',NULL,NULL,'2025-05-30 10:29:18.082',1),('7faa7aa9-a1d9-4da7-9d98-13eca0ae39f0','0d2e2255a2fc71bed06d29e7d904d0b063d19f78b78a87c74557a1ed201bf5de','2025-05-30 10:29:19.276','20250513050513_add_event_management',NULL,NULL,'2025-05-30 10:29:19.099',1),('a0673ee1-b9e0-435e-a8fc-098c989a6ddd','064af90e8ea213a0ab09635cea2025411cccc17be9ed1d5b2ae6d5c18f45a151','2025-05-30 10:29:17.133','20250428021018_add_bank_models',NULL,NULL,'2025-05-30 10:29:16.673',1),('a71f929d-64ff-456e-aaf6-b72ae808850d','7382fbcc931f8fc88468fca94325c6fd130c4cb4b8a271161c17f84edacfeaf7','2025-05-30 10:29:20.614','20250523193456_add_cart_and_order_models',NULL,NULL,'2025-05-30 10:29:20.308',1),('ae408991-dc83-4aed-afea-9053601641c8','6af089bd10ba3d924586bb10e319ff4862de99ab55490034a637b043983773b4','2025-05-30 10:29:18.503','20250503121350_add_user_state',NULL,NULL,'2025-05-30 10:29:18.417',1),('b3ff23e4-6efa-4fea-ad17-a76b2676983d','7a816e356f10f78e12afd9349e967fb5205a6c75187c5ed0db58631f634134f3','2025-05-30 10:29:18.321','20250501000000_enhance_notification_system',NULL,NULL,'2025-05-30 10:29:18.137',1),('bd88665e-c897-4228-b43d-18916d20ba65','298280cfa33b02e8b455dfe0e8fb7c95502eb8f9bf69887e00a99553fd17621e','2025-05-30 10:29:17.240','20250428052726_add_case_insensitive_collation',NULL,NULL,'2025-05-30 10:29:17.135',1),('d616573a-f805-4460-a01b-ad7000d2c3ba','e1878f2e073aa12e063afd4f3173b380f4039ef95d1f8c24ab505445c23a49c0','2025-05-30 10:29:18.401','20250502132226_add_user_credentials',NULL,NULL,'2025-05-30 10:29:18.323',1),('dd293b8f-0390-44f0-a776-51e1687a6ca7','ecea93a3dc3832873a8c9132711cdf62d64a17618d06db1bc45dbb9e65a33d13','2025-05-30 10:32:26.198','20250530103220_add_ticket_hold_system',NULL,NULL,'2025-05-30 10:32:25.486',1),('f10370b9-5425-4659-800f-4e68d149b116','ebf4a18cb7025262c6fa0578868ce771b7bcc95eff4e169e4da4fa6563a4180f','2025-05-30 10:29:20.305','20250523000000_add_volunteer_notification_preferences',NULL,NULL,'2025-05-30 10:29:20.153',1),('fe804a28-f6bd-40bf-8c3e-ad5d42bb9f42','1401dce20c9b0eac8624a2b7521c04be9a4f3e976d9acce1827aa84a55eb7caa','2025-05-30 10:29:20.922','20250601000000_add_product_system_phase1',NULL,NULL,'2025-05-30 10:29:20.617',1);
/*!40000 ALTER TABLE `_prisma_migrations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cart_items`
--

DROP TABLE IF EXISTS `cart_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cart_items` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `quantity` int NOT NULL,
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  `cartId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `productId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `cart_items_cartId_idx` (`cartId`),
  KEY `cart_items_productId_idx` (`productId`),
  CONSTRAINT `cart_items_cartId_fkey` FOREIGN KEY (`cartId`) REFERENCES `carts` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `cart_items_productId_fkey` FOREIGN KEY (`productId`) REFERENCES `products` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cart_items`
--

LOCK TABLES `cart_items` WRITE;
/*!40000 ALTER TABLE `cart_items` DISABLE KEYS */;
/*!40000 ALTER TABLE `cart_items` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `carts`
--

DROP TABLE IF EXISTS `carts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `carts` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `userId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `carts_userId_idx` (`userId`),
  CONSTRAINT `carts_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `carts`
--

LOCK TABLES `carts` WRITE;
/*!40000 ALTER TABLE `carts` DISABLE KEYS */;
INSERT INTO `carts` VALUES ('3b873426-d180-4968-998c-7ed056f1a345','68bc5359-331c-4b28-8ff3-a1e5fd2660a3','2025-05-30 11:27:47.463','2025-05-30 11:27:47.463'),('678b9eac-8af2-49c5-a277-30b4b5b25cda','c1b4d722-547a-41a1-bf0c-45fbbacc9a92','2025-06-16 20:00:58.558','2025-06-16 20:00:58.558'),('7937dfd1-da6f-451b-ae27-06c0d48b075f','3e6244fb-5234-4f31-b32b-e14108f4c3f0','2025-06-10 21:54:14.870','2025-06-10 21:54:14.870');
/*!40000 ALTER TABLE `carts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `event_categories`
--

DROP TABLE IF EXISTS `event_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `event_categories` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `color` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `event_categories`
--

LOCK TABLES `event_categories` WRITE;
/*!40000 ALTER TABLE `event_categories` DISABLE KEYS */;
INSERT INTO `event_categories` VALUES ('1f6872e6-e474-4550-98e3-1be9a418b4c4','PNPN',NULL,'#3B82F6','2025-05-30 10:58:31.101','2025-05-30 10:58:31.101');
/*!40000 ALTER TABLE `event_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `events`
--

DROP TABLE IF EXISTS `events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `events` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `shortDescription` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `startDate` datetime(3) NOT NULL,
  `endDate` datetime(3) NOT NULL,
  `location` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` text COLLATE utf8mb4_unicode_ci,
  `virtualLink` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `isVirtual` tinyint(1) NOT NULL DEFAULT '0',
  `image` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'draft',
  `capacity` int DEFAULT NULL,
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  `createdById` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `categoryId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `events_createdById_idx` (`createdById`),
  KEY `events_categoryId_idx` (`categoryId`),
  KEY `events_status_idx` (`status`),
  KEY `events_startDate_idx` (`startDate`),
  CONSTRAINT `events_categoryId_fkey` FOREIGN KEY (`categoryId`) REFERENCES `event_categories` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `events_createdById_fkey` FOREIGN KEY (`createdById`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `events`
--

LOCK TABLES `events` WRITE;
/*!40000 ALTER TABLE `events` DISABLE KEYS */;
INSERT INTO `events` VALUES ('e40ca619-7d39-4767-87e3-7e83edfd97e1','nassau','asdfasdfasdfasdfasdf','It all tests here','2025-05-30 12:00:00.000','2025-06-03 00:00:00.000','T-LAND','3505 litchfield pl se 97317','',0,'/api/images/cmbap0er10000lhn48xa6vccb','published',500,'2025-05-30 11:01:33.719','2025-05-30 11:01:33.719','c1b4d722-547a-41a1-bf0c-45fbbacc9a92','1f6872e6-e474-4550-98e3-1be9a418b4c4');
/*!40000 ALTER TABLE `events` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ledger`
--

DROP TABLE IF EXISTS `ledger`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ledger` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `date` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `description` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `totalDeposits` double NOT NULL DEFAULT '0',
  `totalWithdrawals` double NOT NULL DEFAULT '0',
  `totalTransfers` double NOT NULL DEFAULT '0',
  `netChange` double NOT NULL DEFAULT '0',
  `status` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `verifiedById` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `verifiedAt` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ledger_verifiedById_fkey` (`verifiedById`),
  CONSTRAINT `ledger_verifiedById_fkey` FOREIGN KEY (`verifiedById`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ledger`
--

LOCK TABLES `ledger` WRITE;
/*!40000 ALTER TABLE `ledger` DISABLE KEYS */;
/*!40000 ALTER TABLE `ledger` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `newsarticle`
--

DROP TABLE IF EXISTS `newsarticle`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `newsarticle` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `excerpt` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `image` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  `publishedAt` datetime(3) DEFAULT NULL,
  `authorId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `categoryId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'draft',
  `featured` tinyint(1) NOT NULL DEFAULT '0',
  `views` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `newsarticle_slug_key` (`slug`),
  KEY `newsarticle_authorId_fkey` (`authorId`),
  KEY `newsarticle_categoryId_fkey` (`categoryId`),
  CONSTRAINT `newsarticle_authorId_fkey` FOREIGN KEY (`authorId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `newsarticle_categoryId_fkey` FOREIGN KEY (`categoryId`) REFERENCES `newscategory` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `newsarticle`
--

LOCK TABLES `newsarticle` WRITE;
/*!40000 ALTER TABLE `newsarticle` DISABLE KEYS */;
INSERT INTO `newsarticle` VALUES ('99e06921-c999-4d4e-b5c9-a89761376809','Welcome to the Bank of Styx','<h2>Welcome to the Bank of Styx!</h2><p>Welcome to the official website of the Bank of Styx, your trusted financial institution for the Pirate Rinfair community.</p><p>We\'re excited to have you here and look forward to serving all your banking needs.</p><h3>Our Services</h3><ul><li>Secure banking for all your needs</li><li>Easy transfers between accounts</li><li>Pay code system for quick payments</li><li>Merchant integration</li></ul><p>Visit our help section to learn more about how to use our services!</p><h2>Lorem Ipsum Heading</h2><p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p><p>Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p><h3>Subheading</h3><ul><li>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li><li>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li><li>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li><li>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li></ul>','Welcome to the Bank of Styx, your trusted financial institution for the Pirate Rinfair community.','/api/images/cmbzipjdb0000lhu0prx3cx7i','2025-05-30 10:30:09.355','2025-06-16 20:00:44.691','2025-05-30 10:30:09.352','c1b4d722-547a-41a1-bf0c-45fbbacc9a92','6b15d5d2-82f9-43d3-8b54-007476d04395','welcome-to-bank-of-styx','published',1,10),('bb1ed53d-5c1b-467c-b7e2-a529d97d17d6','New Features Coming Soon','<h2>Exciting New Features Coming Soon!</h2>\n<p>We\'re constantly working to improve your banking experience at the Bank of Styx.</p>\n<p>Here\'s a preview of some exciting new features we\'re developing for you.</p>\n<h3>Coming Soon</h3>\n<ul>\n  <li>Enhanced security features</li>\n  <li>Mobile app for on-the-go banking</li>\n  <li>Improved user interface</li>\n  <li>New merchant partnerships</li>\n</ul>\n<p>Stay tuned for more updates!</p>\n<h2>Lorem Ipsum Heading</h2>\n<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>\n<p>Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>\n<h3>Subheading</h3>\n<ul>\n  <li>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>\n  <li>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>\n  <li>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>\n  <li>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>\n</ul>\n<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>\n','We\'re working on exciting new features to enhance your banking experience.','/images/coming-soon.jpg','2025-05-30 10:30:09.359','2025-06-16 11:02:29.408','2025-05-29 10:30:09.352','c1b4d722-547a-41a1-bf0c-45fbbacc9a92','4ae3899f-2c21-404d-a220-1b8e5a41be62','new-features-coming-soon','published',0,4);
/*!40000 ALTER TABLE `newsarticle` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `newscategory`
--

DROP TABLE IF EXISTS `newscategory`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `newscategory` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `newscategory_name_key` (`name`),
  UNIQUE KEY `newscategory_slug_key` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `newscategory`
--

LOCK TABLES `newscategory` WRITE;
/*!40000 ALTER TABLE `newscategory` DISABLE KEYS */;
INSERT INTO `newscategory` VALUES ('4ae3899f-2c21-404d-a220-1b8e5a41be62','News','news','Latest news and updates','2025-05-30 10:30:09.340','2025-05-30 10:30:09.340'),('53186b2f-553c-4a9e-826d-54117c694c3a','Events','events','Upcoming and past events','2025-05-30 10:30:09.340','2025-05-30 10:30:09.340'),('6b15d5d2-82f9-43d3-8b54-007476d04395','Featured','featured','Featured articles from the Bank of Styx','2025-05-30 10:30:09.340','2025-05-30 10:30:09.340'),('bbbccc1f-5aae-4ff2-b8c6-daf7dc4d8a14','Announcements','announcements','Official announcements from the Bank of Styx','2025-05-30 10:30:09.340','2025-05-30 10:30:09.340');
/*!40000 ALTER TABLE `newscategory` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notification`
--

DROP TABLE IF EXISTS `notification`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `notification` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `read` tinyint(1) NOT NULL DEFAULT '0',
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  `transactionId` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `category` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `link` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `icon` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `priority` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'medium',
  `userId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `notification_transactionId_fkey` (`transactionId`),
  KEY `notification_userId_idx` (`userId`),
  KEY `notification_category_idx` (`category`),
  KEY `notification_read_idx` (`read`),
  CONSTRAINT `notification_transactionId_fkey` FOREIGN KEY (`transactionId`) REFERENCES `transaction` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `notification_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notification`
--

LOCK TABLES `notification` WRITE;
/*!40000 ALTER TABLE `notification` DISABLE KEYS */;
/*!40000 ALTER TABLE `notification` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_items`
--

DROP TABLE IF EXISTS `order_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `order_items` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `quantity` int NOT NULL,
  `price` double NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `orderId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `productId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `order_items_orderId_idx` (`orderId`),
  KEY `order_items_productId_idx` (`productId`),
  CONSTRAINT `order_items_orderId_fkey` FOREIGN KEY (`orderId`) REFERENCES `orders` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `order_items_productId_fkey` FOREIGN KEY (`productId`) REFERENCES `products` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_items`
--

LOCK TABLES `order_items` WRITE;
/*!40000 ALTER TABLE `order_items` DISABLE KEYS */;
INSERT INTO `order_items` VALUES ('d244b021-1c06-46d7-932c-a48fa69cbb56',4,35,'nassau child','nassau child','2025-05-30 11:29:32.880','b98be3d7-605c-499d-976a-0f403effcad9','027edaf5-e52e-4559-8cc5-aa8a7fcb0435');
/*!40000 ALTER TABLE `order_items` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `orders`
--

DROP TABLE IF EXISTS `orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `orders` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `orderNumber` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `total` double NOT NULL,
  `subtotal` double NOT NULL,
  `tax` double DEFAULT NULL,
  `discount` double DEFAULT NULL,
  `paymentMethod` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `paymentIntentId` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  `userId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `orders_orderNumber_key` (`orderNumber`),
  KEY `orders_userId_idx` (`userId`),
  KEY `orders_status_idx` (`status`),
  KEY `orders_orderNumber_idx` (`orderNumber`),
  CONSTRAINT `orders_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `orders`
--

LOCK TABLES `orders` WRITE;
/*!40000 ALTER TABLE `orders` DISABLE KEYS */;
INSERT INTO `orders` VALUES ('b98be3d7-605c-499d-976a-0f403effcad9','ORD-1748604572879','paid',140,140,NULL,NULL,NULL,'pi_3RUR7yQbBVg7JcXN55iV4kEH',NULL,'2025-05-30 11:29:32.880','2025-05-30 11:30:31.484','68bc5359-331c-4b28-8ff3-a1e5fd2660a3');
/*!40000 ALTER TABLE `orders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `paycode`
--

DROP TABLE IF EXISTS `paycode`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `paycode` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` double NOT NULL,
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `expiresAt` datetime(3) NOT NULL,
  `status` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `createdById` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `redeemedById` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `redeemedAt` datetime(3) DEFAULT NULL,
  `uses` int NOT NULL DEFAULT '0',
  `maxUses` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `paycode_code_key` (`code`),
  KEY `paycode_createdById_fkey` (`createdById`),
  KEY `paycode_redeemedById_fkey` (`redeemedById`),
  CONSTRAINT `paycode_createdById_fkey` FOREIGN KEY (`createdById`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `paycode_redeemedById_fkey` FOREIGN KEY (`redeemedById`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `paycode`
--

LOCK TABLES `paycode` WRITE;
/*!40000 ALTER TABLE `paycode` DISABLE KEYS */;
/*!40000 ALTER TABLE `paycode` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_categories`
--

DROP TABLE IF EXISTS `product_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_categories` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `isActive` tinyint(1) NOT NULL DEFAULT '1',
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_categories`
--

LOCK TABLES `product_categories` WRITE;
/*!40000 ALTER TABLE `product_categories` DISABLE KEYS */;
INSERT INTO `product_categories` VALUES ('58f2de99-301b-425b-be07-7a8d00b492a2','parking','',1,'2025-05-30 11:19:07.056','2025-05-30 11:19:07.056'),('64de9ad7-62ab-4b6c-bad4-5cec621c6d54','tickets','lots of tickets',1,'2025-05-30 11:17:46.297','2025-05-30 11:17:46.297'),('ce9400a9-49ac-4fbd-8d4f-9e617b710ed8','pipes','',1,'2025-05-30 11:18:54.273','2025-05-30 11:18:54.273');
/*!40000 ALTER TABLE `product_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `products`
--

DROP TABLE IF EXISTS `products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `products` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `shortDescription` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `price` double NOT NULL,
  `image` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `isActive` tinyint(1) NOT NULL DEFAULT '1',
  `affectsCapacity` tinyint(1) NOT NULL DEFAULT '1',
  `inventory` int DEFAULT NULL,
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  `categoryId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `eventId` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `products_categoryId_idx` (`categoryId`),
  KEY `products_eventId_idx` (`eventId`),
  CONSTRAINT `products_categoryId_fkey` FOREIGN KEY (`categoryId`) REFERENCES `product_categories` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `products_eventId_fkey` FOREIGN KEY (`eventId`) REFERENCES `events` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `products`
--

LOCK TABLES `products` WRITE;
/*!40000 ALTER TABLE `products` DISABLE KEYS */;
INSERT INTO `products` VALUES ('027edaf5-e52e-4559-8cc5-aa8a7fcb0435','nassau child','nassau child','Nassau child ticket',35,'https://placehold.co/400',1,1,121,'2025-05-30 11:24:21.609','2025-05-30 11:30:31.499','64de9ad7-62ab-4b6c-bad4-5cec621c6d54','e40ca619-7d39-4767-87e3-7e83edfd97e1'),('16addfb6-fbec-47cc-9876-9e620e8c0221','nassau parking pass','parking pass needed for car truck rv','parking pass',15,'',1,0,NULL,'2025-05-30 11:20:37.985','2025-05-30 11:20:37.985','58f2de99-301b-425b-be07-7a8d00b492a2','e40ca619-7d39-4767-87e3-7e83edfd97e1'),('3c786fc4-1f43-4594-8cd6-9274d827170b','nassau','Nassau adult ticket','Nassau adult ticket',75,'https://placehold.co/400',1,1,350,'2025-05-30 11:23:43.632','2025-05-30 11:23:43.632','64de9ad7-62ab-4b6c-bad4-5cec621c6d54','e40ca619-7d39-4767-87e3-7e83edfd97e1');
/*!40000 ALTER TABLE `products` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `support_ticket`
--

DROP TABLE IF EXISTS `support_ticket`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `support_ticket` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'open',
  `priority` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'medium',
  `category` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'general',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `userId` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `assignedToId` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `assignedAt` datetime(3) DEFAULT NULL,
  `resolvedById` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `resolvedAt` datetime(3) DEFAULT NULL,
  `resolution` text COLLATE utf8mb4_unicode_ci,
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `support_ticket_status_idx` (`status`),
  KEY `support_ticket_priority_idx` (`priority`),
  KEY `support_ticket_userId_idx` (`userId`),
  KEY `support_ticket_assignedToId_idx` (`assignedToId`),
  KEY `support_ticket_resolvedById_fkey` (`resolvedById`),
  CONSTRAINT `support_ticket_assignedToId_fkey` FOREIGN KEY (`assignedToId`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `support_ticket_resolvedById_fkey` FOREIGN KEY (`resolvedById`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `support_ticket_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `support_ticket`
--

LOCK TABLES `support_ticket` WRITE;
/*!40000 ALTER TABLE `support_ticket` DISABLE KEYS */;
/*!40000 ALTER TABLE `support_ticket` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `temporary_password`
--

DROP TABLE IF EXISTS `temporary_password`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `temporary_password` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `userId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `passwordHash` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiresAt` datetime(3) NOT NULL,
  `used` tinyint(1) NOT NULL DEFAULT '0',
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `temporary_password_userId_key` (`userId`),
  KEY `temporary_password_userId_idx` (`userId`),
  CONSTRAINT `temporary_password_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `temporary_password`
--

LOCK TABLES `temporary_password` WRITE;
/*!40000 ALTER TABLE `temporary_password` DISABLE KEYS */;
/*!40000 ALTER TABLE `temporary_password` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ticket_holds`
--

DROP TABLE IF EXISTS `ticket_holds`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ticket_holds` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `userId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `cartItemId` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `expiresAt` datetime(3) NOT NULL,
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ticket_holds_cartItemId_key` (`cartItemId`),
  KEY `ticket_holds_userId_idx` (`userId`),
  KEY `ticket_holds_expiresAt_idx` (`expiresAt`),
  CONSTRAINT `ticket_holds_cartItemId_fkey` FOREIGN KEY (`cartItemId`) REFERENCES `cart_items` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `ticket_holds_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ticket_holds`
--

LOCK TABLES `ticket_holds` WRITE;
/*!40000 ALTER TABLE `ticket_holds` DISABLE KEYS */;
INSERT INTO `ticket_holds` VALUES ('9dd45242-bf47-4307-82c1-5e27a60c5566','68bc5359-331c-4b28-8ff3-a1e5fd2660a3',NULL,'2025-05-30 11:44:13.563','2025-05-30 11:29:13.564','2025-05-30 11:29:13.564'),('d3d60dfd-3b87-4fae-8416-2174fac942a3','3e6244fb-5234-4f31-b32b-e14108f4c3f0',NULL,'2025-06-10 22:09:14.874','2025-06-10 21:54:14.875','2025-06-10 21:54:14.875'),('d9373141-9f8c-475e-83e7-e5530cb1b204','68bc5359-331c-4b28-8ff3-a1e5fd2660a3',NULL,'2025-05-30 11:43:38.720','2025-05-30 11:27:50.722','2025-05-30 11:28:38.721');
/*!40000 ALTER TABLE `ticket_holds` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ticket_note`
--

DROP TABLE IF EXISTS `ticket_note`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ticket_note` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `isInternal` tinyint(1) NOT NULL DEFAULT '0',
  `ticketId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `authorId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `ticket_note_ticketId_idx` (`ticketId`),
  KEY `ticket_note_authorId_idx` (`authorId`),
  CONSTRAINT `ticket_note_authorId_fkey` FOREIGN KEY (`authorId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `ticket_note_ticketId_fkey` FOREIGN KEY (`ticketId`) REFERENCES `support_ticket` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ticket_note`
--

LOCK TABLES `ticket_note` WRITE;
/*!40000 ALTER TABLE `ticket_note` DISABLE KEYS */;
/*!40000 ALTER TABLE `ticket_note` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tickets`
--

DROP TABLE IF EXISTS `tickets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tickets` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `productId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('AVAILABLE','HELD','SOLD','CANCELLED') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'AVAILABLE',
  `holdId` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `orderId` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `seatInfo` json DEFAULT NULL,
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `tickets_productId_idx` (`productId`),
  KEY `tickets_status_idx` (`status`),
  KEY `tickets_holdId_idx` (`holdId`),
  KEY `tickets_orderId_idx` (`orderId`),
  CONSTRAINT `tickets_holdId_fkey` FOREIGN KEY (`holdId`) REFERENCES `ticket_holds` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `tickets_orderId_fkey` FOREIGN KEY (`orderId`) REFERENCES `orders` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `tickets_productId_fkey` FOREIGN KEY (`productId`) REFERENCES `products` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tickets`
--

LOCK TABLES `tickets` WRITE;
/*!40000 ALTER TABLE `tickets` DISABLE KEYS */;
INSERT INTO `tickets` VALUES ('002a71be-2b92-4b57-9ab5-0b2f9c905467','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','HELD','9dd45242-bf47-4307-82c1-5e27a60c5566',NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:29:13.565'),('002dbb7c-f174-4db4-8c43-8597c3b2afd1','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('0074996b-6fa9-4c10-8c8a-aa040d882aa9','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('0124a9ad-1945-42b9-99e4-c57969ca0f8a','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','HELD','9dd45242-bf47-4307-82c1-5e27a60c5566',NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:29:13.565'),('01745e7a-14ec-4357-998a-8f2c3d8f23e1','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('0235281c-1363-4b4c-ba9f-7457209e770e','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('0343abcb-d1ab-4a70-bff6-3cac63aa62c4','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('039cc35f-432f-4f78-bb52-beefdafeb381','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('04710ec5-4582-4bd3-a580-8d39f4493f46','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('049fb7c8-478b-41a2-a0c6-47f2009a8348','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('04a1af06-d5da-49a9-9450-66f6ce1dc05b','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','HELD','9dd45242-bf47-4307-82c1-5e27a60c5566',NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:29:13.565'),('04ace957-24b4-4a76-b288-ed33a863e722','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('0520c3fd-abab-4627-9848-cee8e60835d2','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('0631a8b4-7f2a-480f-a6c3-0d4bc96e5640','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','HELD','9dd45242-bf47-4307-82c1-5e27a60c5566',NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:29:13.565'),('06689c11-a291-4777-8844-9100484b5886','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-06-10 21:54:32.705'),('0760f860-91ff-46cd-b770-414bd48b939a','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('07c34591-21ed-48a0-a8d4-e8965406d483','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('0b57582f-232b-48ef-b65b-e007216b4573','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-06-10 21:54:32.705'),('0bd37f76-3c0c-4283-ae8c-ff4aea0a7f7d','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('0bfb3db1-5981-4aee-b820-d49bb48943eb','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-06-10 21:54:32.705'),('0c9e82f1-ffe9-4006-9d3a-0758fa05e04c','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-06-10 21:54:32.705'),('0d093450-2e37-4920-8b5d-8820e4be8288','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('0e68a24d-2a56-4c44-af63-d8df888561e6','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('0e874c49-3ea3-4f39-9507-167ad4f19945','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('0eb06bee-3f57-453f-86f6-8190a68eb45a','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('0ee12571-21af-4fec-b4ab-4748e78368a3','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('0ff33041-5e4a-4a33-84f9-8637f800f855','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('10762e9a-2828-4fa5-aaf2-7a9f05fa497c','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('1084b469-3b90-49db-8520-0765ae31ccea','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('10ada66c-0895-4caa-8ee1-5068d3b87e6a','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-06-10 21:54:32.705'),('11ebe6f4-e0fb-4636-a21b-948edaba5871','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('1243b67e-ead9-4d55-94af-01e79a393f2b','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-06-10 21:54:32.705'),('14ab7350-683c-46be-b888-4f05500b8d87','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-06-10 21:54:32.705'),('152c1abf-77ec-4fdc-a5dc-73685943f485','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-06-10 21:54:32.705'),('156a7ced-5bca-4515-bc55-572d0d6190ee','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('15ac3a98-82f6-4af3-a13d-fc8c92ab7702','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('15e5de42-9a85-4aac-9578-f72f8fc398fa','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('1661f294-3be0-431f-add8-0477f120483b','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('168a703d-d55a-4391-a3ef-722867041698','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('177acd88-345c-4ccb-9f62-0074d7bcfd35','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('1892a683-fbea-484f-b148-3237cf1dffe1','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('19826314-fe00-410a-bae5-a64660a52fe4','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('19a0315f-4bda-4865-a63a-c0392f1564c6','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('1a643901-5ac6-48cf-bbbe-0e1180ccaa21','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('1a90ac20-0086-48db-b247-26412fad7220','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('1a9cb9a0-b682-4310-ab65-47494b4c4d0f','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-06-10 21:54:32.705'),('1aa899de-67db-4f5e-a840-8f7ad82435ab','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-06-10 21:54:32.705'),('1ab44d51-0148-433d-8cd1-58f921a88021','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('1aea3558-2d28-4415-93db-b03d957ad3c8','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('1b142cf7-0a55-436f-b845-d509ffa35d29','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('1b23b2aa-2778-4bd9-b663-1b493cbbc7aa','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('1b492402-5926-44fa-bf35-3a83dcdb59b8','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('1b66681b-898b-460b-b9ee-09ea8e415e81','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('1b8606ec-04be-489a-a3f5-c9a6026d2e9e','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('206739f1-c29f-476b-848b-7dea9c3a813a','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-06-10 21:54:32.705'),('21612a2c-b666-4b85-b87b-d8737bbbe10f','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('220409e3-7c73-451e-a453-90207f1f83d3','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('2270c4f6-b764-4218-aed3-29c75c67733c','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('22b4edba-48b4-43ad-a909-9b69e3607917','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-06-10 21:54:32.705'),('2325ccf3-fd1e-45bd-a99d-0bbb835211e6','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('23d7c809-fed6-4f03-87ca-d2973effc183','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('24b3073f-26b5-470c-ab05-1bdeea0b7d9e','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-06-10 21:54:32.705'),('25ea2431-e97c-4d31-801d-8d78ae96b708','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('26a9e076-9a4a-4cab-9096-87371f140fea','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('26b6b81c-b988-41d6-a819-d745d798e039','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('2a2b5ea5-477a-4cbc-9508-f7be11b3416c','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('2a7b9237-bdd0-45ac-adbc-ecc615ed9cb6','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('2a8d1e72-caa1-41cf-be07-d1e0a819cb30','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('2c38d21d-6bf0-4f65-b321-9dc8d94de4fa','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-06-10 21:54:32.705'),('2ca67cbf-022e-452c-a4e7-6767c6564c8a','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-06-10 21:54:32.705'),('2d58a1f2-1bd1-457c-bbca-679f544bbe97','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('2d8e0f70-a53a-4fc7-a876-a6efbeff3cc5','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('3070b21f-4dfb-412c-ada5-d31110f262d9','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('313586d5-40a5-4fea-bc08-ddabbc4ee994','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-06-10 21:54:32.705'),('315828c4-aeec-4f44-a1a7-fb83a957eb20','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('31a26d64-dc14-44d1-986c-df5a392c5e9d','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('31c2f4c4-978a-41cf-b11d-20fa146d86a4','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('31e980bf-7a21-481f-86bc-bfaa15cc9ad1','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('32498989-ada6-4734-9d18-b9a3e1711d1b','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('328bc996-3499-4558-8c24-f37e709ad680','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-06-10 21:54:32.705'),('32beb921-20a6-402f-af4b-b65bcdda0c60','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('32d077e4-02d2-4aec-8a70-236925a88b76','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-06-10 21:54:32.705'),('32e339f0-e182-4b9e-a141-8e20a1655a4e','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('34977995-8058-47b2-b104-68862c305a86','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('34a49ea6-74fb-4ff3-ac9e-0c984f37a038','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('34a94954-d096-4f30-b0f4-e446191ec8a1','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('3568d1d9-a2ee-4874-a9f9-12bb5d349b56','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-06-10 21:54:32.705'),('35917af8-20cd-48b9-9557-55796acb74a4','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('35df0cdf-039c-40a0-a8d5-03b27a51b5b5','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('364b589f-bbc6-46f9-9967-f8dd4afdbe78','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('3780c5f4-5152-4ba6-aaa7-f840e1d06fd5','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('37f595c5-2351-4126-a1fd-79f45beb3827','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('389a30cb-f551-4ac2-b3ae-81410bed8216','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('391be946-1b6c-4d19-a60e-799cdf715108','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('39b0ee71-6a30-409f-a227-14fa6aaf8e8b','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('3a2a3615-d3c1-48e2-ae6a-cf033012bf2e','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('3ab88718-6369-440a-a551-b8742d140c10','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('3ace894e-82fb-43ab-9e57-b3a3b92f679e','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('3ae5386e-4326-4447-8f37-64e736cc8aaa','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('3b971940-2f58-45d3-8441-5e6d8036471c','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('3bac49f0-7d99-4fe3-85b2-284ab406822c','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('3bb9bbd4-0430-4c16-a005-cd8a3e7f67a4','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('3bfd7c57-8dc6-4f06-9692-b09700605868','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('3c01d451-03ae-4b2f-9da2-ebdbb6355e50','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('3d698649-ae0a-4ef6-99ca-dce70169dbcc','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('3d9d4f6a-f09a-4309-9abe-e223227bfd13','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('3fba7c8d-1418-4d2b-8651-2fda06c71be8','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('40fc3b4b-bc5e-4b17-9377-3d20f4037a53','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('4169747b-7249-4f4e-993f-8589dfe88e42','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('418954ab-43d2-4e53-9af0-7288fea6fa8c','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('425656af-7669-436b-a947-6b2ca71d1f13','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('42f1fc93-12f5-4c3e-aab0-755a914a9557','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('432ff4ec-cb5a-4465-94df-ab0e5865b1ce','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('435114a6-f01b-413b-9f1a-d846bb65d8bc','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('4359ebfc-4d76-49d4-9394-ded178856481','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('43741167-b471-43a9-bdf6-1f93825dbffd','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('43a2d90c-c8f4-4675-997b-9e87bc9fb254','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('43b20a66-1e69-4dfe-8ea0-90494e830283','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('44867566-6af0-4329-a868-69b8d5f1f381','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('452e48a5-49a1-4080-b70e-9a0609c984a9','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('45aed28f-75a6-451b-8d5d-4ff78afd1f40','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('45b74bac-1d95-4495-b0e1-2d1ddabf3b66','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('46ba6619-3889-4749-8ba6-c7a31cf47d7f','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('470e3c8b-6a16-4926-a212-bd79110949df','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('476fd58b-8c8c-4084-8580-b83346354ea7','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('477af543-7fa5-469d-a3cc-4c9a7903061c','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('47b1e7b1-f330-4668-b5f4-e4b1c077ae6d','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('47bfde0e-95ef-4afe-9caa-6e17af729a9f','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('4812a520-a038-48c7-b0e4-83363b5dc2de','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('489991dd-e232-4867-9120-568b1c15d33a','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('48a2ee7f-8201-49ed-b9f9-60bfdbb3c646','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('48f50e84-a425-41c6-9da9-acca807cc3d2','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('49dea9ac-4764-45ef-ab33-a390dab0940b','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('49f75807-a0f3-41a2-8d3b-d7792852820e','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('4c42e19d-4571-47dd-adeb-44070c8fa49b','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('4c4b0970-58b6-43e4-b63b-f1c3c04b7647','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('4c64d8f1-81e9-483f-a3d8-dadb8dd3f24d','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('4caea48c-0618-4ffd-8d26-0c822b5ead8f','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('4e931bed-693a-4ba9-ba82-353f709d3610','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('4e9e1d06-f5bb-43d0-97b0-82ef41e7af83','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('4effefad-293c-4d9f-8951-13add60c0f3c','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('4f31d778-862d-475c-b76c-6d98c785c272','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('4f67870c-01bc-4bb7-bc0b-120cac9802b0','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('4fab73dc-322b-4cc7-af97-590c08683b39','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('504d084b-e0bf-428f-96ce-104074b9544a','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('506e74d7-5033-4ea1-9c19-2beb8912440a','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('5070803f-663a-4216-a5b1-c28f1f348c53','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('51c5c26f-7dd8-4b67-bb3b-eca277e50713','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('52520c8b-1952-47e4-9869-0db809fa926b','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('5313baac-8af3-4d58-9d6f-d54a8d17d7fb','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('53aaad1a-2edd-4791-8b39-a2a374ed29c1','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('53abde5c-0d14-40f9-974e-960e19b849f8','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('541680c3-0f37-43a2-ad33-cbed7a17faf3','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('54be074b-7e1f-43af-88ed-ee7752e2f3f9','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('556212de-c648-4de3-b362-31a508934437','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('55901612-34e7-4ca7-9f5a-7201bc4cddc0','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('559497b0-2992-4bc7-a915-92775c02acce','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('56c9376a-4668-4f9a-89e6-a3d43b0726b9','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('57a96f33-d2c1-49ff-a7b5-477dc341d555','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('57d6292b-8bd2-4b3b-8ebe-e62c6a9bbedf','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('587b7716-13e1-4b32-a5ac-a4befd5b6577','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('59452d1c-2892-4a5a-a77d-ac086730fd65','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('5a419f9d-ae8f-456f-b19a-a2175fea0458','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('5ae2c3a9-4709-40d6-8047-05174a9b1d9b','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('5c18740c-d842-4b1c-b6e4-9c7dd46760b8','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('5ca0209c-26bf-4413-8419-95a6a6760055','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('5ceb09b9-a93e-42f6-84aa-68b14ea78808','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('5d81e6ac-c0a7-4f51-93fe-e5f55f745d61','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('5db40d01-f079-48a6-ae47-4b03cde82696','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('5de1479e-77da-4a27-9a09-a132f528e8bb','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('5e7854b6-be04-49e8-b7ac-eeffbc57ee22','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('5eab05b5-0878-4bc6-876f-fa1fb6155a93','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('60b55282-ef35-4f1f-8adc-1ff155bf0865','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('616d226b-be8e-4f21-9708-b2041f2f7346','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('61fdd506-7b6d-42c0-be93-fc70e8c7bf4d','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('62d8d99a-bb09-4ee5-aed7-3311a8344ffd','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('62d95053-bba1-4263-abf5-cfc15bff0671','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('6375a873-35ac-4eb1-93fd-81d27232b9a1','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('639be674-5b8c-4338-aa77-bb06609274e0','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('63e20980-95f8-445a-9efb-3cf25b6fc9bf','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('6423b2d7-a12b-4488-bca6-989f13f31ade','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('64cb3b7d-590f-4bd4-aa4d-e1cf88974618','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('64d6a986-3429-4ebe-b5f7-4d0706e16fa2','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('6546e4a4-7cf6-46a1-8505-b29ecfd7cdcb','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('658b552e-1b1c-4d53-bb2b-cbae05859de5','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('659b5425-f2dd-4ff1-8b33-3b68aa9c041c','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('6630e2d7-0058-4303-8651-db79dd17f217','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('669e3f91-b41a-43d0-9823-9639e0507977','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('6789a4cf-dfe5-4a74-a52b-c76b73fb7472','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('67cc9a94-8116-4776-b426-9b99ceb8759b','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('684bae7b-7307-430d-bf12-ad021c8bf1c8','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('68e4e667-78c5-4428-b2e6-2a1606d88fbf','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('6bdc84d3-0e40-4436-8c5e-70319328ca59','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('6bdce01e-df4e-43e0-8542-84dfd6591bad','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('6bdeb089-4539-4c1e-bcad-bde2a28532c4','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('6c1c26cc-14fc-4a0b-b0fc-0b64a51bf6fe','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('6d3452e8-89ac-4d2e-a1fb-b816f55aa559','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('6d9e4630-091c-418f-aa91-d99fea43a176','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('6dbff45f-3879-4c71-a9a5-eea577989b2d','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('6e521bd4-387e-477e-9e3b-bf40aeb14a71','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('6ed47471-8b58-4c68-870e-89365a159a1b','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('6f16d0f4-c24d-47ae-ba4b-9fabf1cd515b','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('6f769e9e-377b-4757-aaa6-b4b902cce322','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('704f769a-3b2d-4596-9b22-d608b2c76993','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('70816f5c-7627-4b55-95ee-994f47019488','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('70c69129-34b2-4287-88d4-59f1c7d8a85b','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('70e87204-1200-4134-9d11-4331d8a7eff1','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('71338b9c-d4d0-45d5-bef3-de2a40654189','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('7135cf91-83c8-4784-a5a9-c97467e733c6','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('7190a027-3000-4c09-9b60-9a09aa3db3c4','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('71978a3d-bdff-49c4-95d3-1703cf4b4756','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('71d6efca-f873-40e8-b6cc-3e917ae8314b','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('72485221-616c-4d76-83af-192ebf383f8d','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('728a28fa-d7d6-40b8-bc19-478d859edd40','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('72ae5678-d83a-49ba-a476-86f721456d6e','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('72c3dac8-3c85-4abb-a3f9-77788256539c','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('72cd46f6-e812-4178-b33b-18d1aa4ed195','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('733f2ef2-8e5c-4369-b6e1-c082372cc55a','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('73b08076-7a19-4882-a7fc-5122aa991568','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('740b4eab-c29c-4b99-a4f0-4fc138cb96e2','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('74571979-6443-46d1-96ba-9558cff3bc2d','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('7480bbd7-0225-447d-93cb-aea1acb93571','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('7555d3b8-2cc1-437d-a461-2cbabb859e29','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('757edaac-f63b-4d93-9c34-d1ef8811a419','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('7648dc17-b0fc-4c81-b3e0-9869029449b1','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('771838a3-52e3-4a24-b382-8c52a39174d0','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('771e6499-c31c-467b-ae49-d4edccee2d2d','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('77b49daf-08d0-4a9b-9cd7-72016fdf76dc','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('78536d40-5273-47d1-9edc-313f9bbee33f','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('78e6705e-873e-4dc1-b3f6-6e49b5e2e4c0','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('797fe512-d74d-4fac-a550-240cea6d2021','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('7b34a43d-659e-4919-814a-a864ecc88002','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('7bb5edbb-59c0-410e-9582-9992d445f4e3','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('7bf640d0-6665-4894-aa5b-fdc412cc6c03','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('7c2dadfd-1e33-4917-9827-73a63caec6f5','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('7c9b0591-7a9e-46a6-b4b2-5dc19759f31d','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('7d7097f8-ec14-47ec-8d19-9fe1b8ab5e6e','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('7e897081-6687-4e0f-97d7-ec7dd9cc1fc6','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('7ea9f73f-6b94-4316-ae47-7f29893c8c05','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('7fba8f97-d657-413b-a966-6ac6e675fa65','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('7fff8fbd-13b0-430b-8363-ae7fc49f4a5d','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('803fe1d7-c985-4d71-8a2d-b800056b2a53','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('80d68367-f90b-4e52-abce-4c3462f9fbdf','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('80f07174-cb32-4070-945b-702b89adcc5e','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('81263d12-5bdd-4b14-88b1-bd55a9afd3f7','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('813acc5a-a7ac-472e-b7a0-83378dd89bee','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('8172f4f8-9fd0-4f94-aefc-6ea0dad61a10','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('81a85131-bdc2-4f9a-844e-7ef04c9a7b06','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('81b474a4-452e-4e71-8196-5862fa1231a3','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('820cdef5-98e5-48a4-832c-67400bbbd689','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('82585e82-0a32-43b9-96b7-d6f61f14c87f','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('832441ea-a04b-4874-bf95-e15840c9f115','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('8358a37e-ef59-4a20-8aee-3692762111c1','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('83e7107f-ea66-4444-95a0-69c7a788b8c5','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('84dd6288-53d3-4117-acbd-fcbaadbd7602','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('850d1db1-2299-48aa-a71f-d06317af7c7c','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('8576047f-c5b7-4cb9-8f9f-649a25fad21b','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('863ea35c-b8ac-44a7-9698-8c822c4ac716','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('87dea8ee-e050-433e-8fb1-6a3a51c26858','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('87df9d1a-80d8-4e5f-9b99-841cec42eaea','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('892e137a-35dd-45f5-a989-bb79cdb71b0c','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('89832182-ca54-441a-ae48-2d0834d164ec','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('8a1c2f39-7d58-45a5-b0fb-045f977f1b90','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('8a5bda81-83aa-4f60-8fe2-6a541260853d','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('8a610730-a76e-46d6-9644-d19b9d9f3299','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('8bce506d-40c9-4a21-a560-707f3a6786b3','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('8db10945-5331-409b-a464-adb8f9f67dec','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('8f93e0b6-a75e-4609-b7d2-ae52c3f89d2e','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('903ec839-3358-450b-89b3-3daf3baeba4d','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('90fb7221-8cf8-45bf-ac10-9610a6ca9c1f','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('911fa72b-6e3b-432b-ba00-60da3865a046','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('91b1a669-cb06-443d-9b6d-3cf702f22c90','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('91bbb39d-e37a-4372-8868-5b41a2d6808d','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('91e6bce9-430f-4df5-9486-f528962b61a3','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('93c7b91a-0c95-4d35-938c-4c90bbe51bbd','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('940470f5-c083-4c59-9b2d-0d2eb8d4c0b6','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('950a22eb-7cb6-43bf-bae1-66c925c56846','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('95719d08-8b58-42e4-bc6f-c9161d7de689','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('95ecf8af-c73c-4936-90ee-17f08be789a4','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('963b9e3e-cdbb-4832-b6e8-1ff441644f3c','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('972ac3da-4d4c-4833-874a-0c98d598af3b','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('979f3ade-b6d9-474b-8976-17c04f6611bd','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('97a7b701-e508-4aa6-84e7-84bd7d707bfe','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('984e66cf-03df-42ee-88fd-c71878ca39a9','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('98bdd982-732f-4ca0-b0f0-be44a874c2e1','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('99de4e69-27bd-4787-bd5f-d06db50efd28','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('9a3ab37b-4458-45d4-a5f0-f9c91fc7400c','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('9a5cf3b4-9bb7-4e98-be82-c3937816b2c1','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('9afe46fb-0b42-416b-87c6-9f88fe666d05','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('9b8e1a3e-66de-459b-a4a6-0e80e6511cc7','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('9cafb90e-a23f-4135-8916-58a260e5fc13','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('9cf7a9c2-30da-497e-b3f5-b56828071267','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('9d68cda0-a0a2-407c-8719-0bef0ac7eb0e','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('9d86d757-f864-4456-be86-ac748c8ef34a','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('9dc63187-4eda-4249-b5bb-0ea28bee3096','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('9de2d038-9192-4507-9f91-6ecdc0d94aeb','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('9ee6d19a-a62d-48e2-9e00-0be31b5f8a9c','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('9f5d5381-4931-4e5f-acb8-1cf83a2880cc','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('9ff8d573-b6c9-4359-95ef-d7e4b9533101','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('a0721557-e4f3-4102-8db3-ea4325af5373','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('a07dcbd9-5dc8-48a1-8253-fb2c6d3683a8','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('a0cbbefb-12f5-4d5a-9635-7ea6175aaf07','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('a0e57869-fbdf-40e4-94ae-11b27a3a64c3','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('a14a69c2-7a35-4883-acfc-3793c3ee2a23','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('a2bfc49a-ed68-416b-953f-e3c3b9f5f730','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('a2c769ad-442a-4a66-8633-e249b1243774','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('a2e682a5-7ba8-4648-8869-d6142077b97f','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('a3502dd3-61ec-463a-b8c5-f19a470a2a52','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('a506f984-1125-4431-88ee-af2a1895d1d2','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('a52cc2fa-fe47-4fe9-94a8-4dd4bdf43a80','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('a559f74e-9f93-4be5-b18e-0daab6575aca','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('a5f81995-e7dd-4095-9df8-846c6f5f7010','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('a6c5a4df-7779-4b6d-a0ea-944593685e70','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('a6d9594d-4398-4328-991b-773736a49304','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('a6f7ca0d-791c-45aa-9ac5-7c70b815280b','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('a7395789-7826-44ed-819d-89d46d0f608e','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('a74c2e23-626c-4763-98be-46460f4c4b7d','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('a76f5497-f8b6-40c4-a031-85542b1b7445','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('a7b2b8de-4abc-4f1c-88fc-328d8dd021fd','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('a82d2693-9ad5-49a6-a42d-e744ba3a137f','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('a9ffe6d9-17ee-48f3-97e9-3883f843b6d7','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('aad5c1f8-7f7e-4f12-b3e1-40603ece20e1','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('aafc8265-a1e6-476f-8ba5-4238636d5bce','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('abdacf3e-41ac-4f66-8dc5-da31da4920d1','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('ac1f1d4d-6f27-4c72-8441-2aa92161572a','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('ad787867-aa93-4ba3-92fc-4e9a0546cca4','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('ae4b8ef0-5c6b-4266-8634-e4d704033bf4','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('af2daa50-2eb5-4607-a229-d017f18158ec','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('af5e9c19-4e18-4d26-a2a5-200ac033b1ba','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('afeb7685-c020-43d0-b5c7-a3ff4b7fefed','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('aff8d03f-ae19-4bf7-9b19-2a3d1003f1d8','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('b14a696a-ceb9-420f-ad04-8becdde4e4b6','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('b1b38dfa-4a28-42e1-a304-ff27af96e876','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('b2aff131-1314-4244-8cd9-9c246de3c406','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('b2cf1e1a-1a99-46b0-b509-ce3cb6741989','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('b2e9f5e0-6f43-4a5a-b0ec-0f5278fc8cf7','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('b34bad73-28d1-431d-a5aa-4c63babbd45c','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('b3f42665-f53c-4e86-9b2d-c5f4a9870bbe','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('b63ba15f-a678-4e46-ad90-8c252f05390e','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('b6e3e965-229d-4f9a-940e-4992f02da65a','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('b7091e16-96d9-4d41-b604-62070d8bef3d','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('b830b86d-3bbd-462c-bd09-ebdcb71969bb','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('ba97f70d-ec4a-4e0c-b64e-9494947f42b3','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('bb1686fd-fd6b-4be3-8cf7-1b361045c686','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('bb1c66a3-0be0-4cd9-8c16-f7aa92dd15e6','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('bb809545-3ac5-4e5b-b95f-82c0798e724d','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('bbb9adfd-c2e4-43a7-8177-499c6c34d6d5','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('bc075f32-6ace-411a-960d-e99b92ce5a67','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('bc7a2213-daf4-44a6-b1fd-cf259eefbebd','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('bca45d22-026e-4fff-9504-80de3e52d8b1','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('bd0c2789-8e6b-4d91-9410-d04c02dca6bc','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('bd396c2f-39d5-48ff-adc4-f41230937518','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('bdd20eed-3d8a-4a59-bf2c-578d759009f5','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('be9ad6ca-a7ba-4d55-aed3-c8e17417a24f','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('bfa9ca0a-fb9f-459d-8342-51a62454915a','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('bfacc205-c716-4bcf-a455-c3a300586c76','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('bfd750dd-0020-4907-8379-50a2d2fdd746','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('c311dc58-3edf-4f2e-b7d8-360fd165cb65','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('c436aedc-6dff-43db-924d-7333e1aa88f6','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('c4491c83-e7b4-4537-ad81-7ad5a6bd895c','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('c563c809-3c2f-4181-8800-5b8a92a1eaa6','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('c59b8bac-45d9-47df-b972-ac8afb281945','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('c5b9d888-2653-47f4-8cdc-a0b26e381c0e','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('c64ad490-58ee-43e8-a04f-0148a28c6d3c','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('c6678c2a-4dca-4ee7-ab55-e5fd5e2e763c','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('c672c3c0-63a2-4e67-aaf7-7d8e7fd15b97','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('c67f0258-1b52-4584-b3d8-e0fe44e0c87b','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('c6b338c4-3a9a-475d-8b74-80f2087cb114','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('c76b04d1-fd58-495e-8112-8fe7cb80c835','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('c853ec08-146e-4c01-8673-a82556422bd3','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('c9500119-5224-4a4e-bf99-5d7707dc8cde','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('c960fada-3174-4ce2-a238-5ea878c05c4f','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('c9871c95-5c21-4adc-ae3b-016b11b73793','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('c9ceab36-5a84-4f0e-86dd-cb20336a4836','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('c9d3d574-c06c-47e3-af12-81b9525044cb','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('ca579148-3355-48ff-9011-26016cfd6767','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('caf2df53-55ae-4d71-8a3d-cf5b0c121291','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('cb45f764-6c69-4141-882a-70db9f193998','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('cb5651b0-3a72-40d9-9052-743123b28911','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('cb6567fd-57be-4eb7-84e7-4d018dceb510','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('cb81349b-6904-4263-aaae-f99cf13054fe','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('cce5989a-2806-4908-b902-d2dae5972a0c','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('ccfa0a1e-a823-47aa-afab-c95e530b76e2','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('cda47874-7068-476a-a50f-243c960491ea','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('cdc0bb4b-4dbc-454c-8623-4c5f9ec5c583','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('ce31012d-014a-433c-954b-5d5e66949d1d','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('ce58b122-dd52-46df-a55d-e5de386d8b24','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('cf7448dc-cc6c-499f-ad7f-4870318d96a0','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('d18a285f-164f-4fce-babc-4dc84a73823c','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('d18c41ae-cfec-462c-b6b9-824dad7e3493','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('d1c2872e-0e92-44ce-9004-2bcf33e32e47','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('d1c497fa-6ffd-4311-a8a2-52ebbdfbf0ed','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('d1c6734d-7172-4d5a-94ef-3b39606f8a64','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('d1d4c503-47c4-441b-a448-578991788484','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('d1ef316b-9ddc-4bfa-b630-5db3c237c31a','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('d2439061-0c57-4573-b1ee-226e35899cb2','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('d2735f82-5da6-41a2-9c53-9641b067231e','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('d2e796fa-448b-4486-8a68-2646338331c2','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('d30ed674-fc7a-452d-b035-b7b35cd4efc7','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('d3461333-f077-4be7-9c6a-f0101be3fe58','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('d3844f81-5ae7-4e0e-a145-b1d7ccb21fc1','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('d4e89227-4df9-4d1c-926a-1c7bbae73660','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('d5aebd9f-34bb-4467-b7bc-dafea91a1843','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('d63946e8-2d06-46c6-82ce-9d1a9f1d8a15','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('d66a9070-6bdb-4354-aab1-f3d60023c655','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('d6862f33-1800-4d5e-956b-026ac710c33a','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('d7569c82-fe2b-4bd9-9cfb-7efc73714906','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('d8f36b3c-ac0d-414e-bf13-ef2add37910f','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('d92aa5b6-1436-4100-bc01-55105803284d','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('d966e48f-92c8-4dca-80ff-3554fa027c83','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('d9d36e48-221c-48d8-aa9c-048d2c259bb0','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('daaec1a2-8c93-40eb-8d94-819f6ecd2f4b','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('db86420a-cccb-4259-9ea7-6951d054b686','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('db8cc5f1-b597-4689-bdf2-edeccbd7eb54','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('db9be1a9-d6eb-4712-a6c3-37c087cf6bfd','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('dbc7185f-5418-40f9-bdeb-c8f715173bec','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('dbd5ba29-38b7-4f82-9563-275e5d4909cc','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('dea039dd-17b0-4699-a666-3dc3fa8a28a4','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('df310217-d083-4304-a8f0-1d2bc8556be3','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('e1c80977-bb96-42ab-bdc7-542f1caa0c12','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('e3f085da-bfb1-4277-b7f3-c0ddab944fce','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('e44402e8-882f-406c-8049-e36d76db2e4c','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('e510df08-ec4e-4fc8-aa1d-515bb61ba725','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('e6c1617e-9898-4c6b-961f-d111e9021a0b','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('e83b408f-dc3c-416f-8fbd-6fb0882f437a','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('e86ecc5c-43c1-4b04-a174-2f6dc546eda5','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('e8eb3544-1911-4f00-bc9a-1a80ea95c17b','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('e9d5e886-77fc-4690-b2c6-37d84036a164','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('eb4f9f0f-e222-422d-bb5c-5c8f4294c863','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('ed4a0a22-295c-4d46-90c5-86284958c3a7','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('ed4f01ba-2bf5-4e13-beea-879ae03e97df','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('edc3c244-f182-4f8f-9457-33806fc4ef79','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('eebaa92f-b0dd-4074-a2c7-ad4361586cbc','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('eed17df8-78c9-4f85-9a30-cb01885ee915','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('eeedc824-69ca-4760-9512-654b2d559c0e','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('ef1bb5ca-f89f-45a5-b891-894b7aaf7b97','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('ef35b46c-73e5-4e97-a030-f0f2f7a79e5d','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('f0afa032-ce82-4180-b6f9-96f1958296bd','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('f0b63782-0b84-4b55-96d1-a86fb7730af4','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('f11d200e-5be1-4eb0-a6b1-203f4dd24fc4','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('f23d6bc2-24b6-4c97-b513-e125bae6f1c2','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('f2f48a76-4f0e-458e-a157-76d29b5761c5','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('f31beb55-7e61-4998-a629-51e562711fa4','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('f33251ac-54cb-4d41-8b41-fc38617d4605','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('f34ec009-d999-421c-8ccf-39f4338a541d','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('f40264b8-77e6-49a7-b3a5-b8d08477a485','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('f4918df0-bef9-4715-bbb6-e5b1e4a19c95','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('f5098c74-aaf0-4045-82e6-63a2659c5cbe','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('f5ebfde2-3869-458e-b32a-8aa540028310','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('f62f458f-a976-4010-a718-43f188818303','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('f6a8e009-11eb-49a7-b0a1-fc9349a569b3','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('f7eed1ca-e00a-4046-aefb-6bbcd44c033d','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('f8564a1f-7ca0-403c-a5cb-e5f23844b6f9','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('f8a9a480-964f-4352-b92e-f2d03d48ba7f','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('f8d75434-7672-4984-8432-3cd44bbeb9ad','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('f94a424b-c171-42eb-a752-3e25d522110a','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('f96ee42b-40f7-44b3-8382-ba260ee262d6','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('f9979564-0061-4f0f-a170-6a578569e80b','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('f9a981f5-6464-4e1c-bace-889c1630a60a','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('f9b94f8d-c500-4468-a822-ed3509917dcc','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('f9d0c640-d5a0-4d73-8dc9-23e61932b32c','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('fa03439d-cd87-4e1a-a76b-047f02693b99','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('fb3dbc24-d1f1-4bc0-9f04-54eaa8ef1bb4','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('fb8bc5eb-4900-44a7-bab4-4f26b2d7d0d5','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('fba94f32-7fab-49f0-ab4f-6aa909bc8d4c','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('fbfc8feb-dd97-4257-b976-892c47e6e276','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('fc4d614f-e6d0-4975-99de-89b459186bb8','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('fc5ae243-9a9f-4787-82fb-f357010c2d98','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('fd5de67e-4a82-493e-9541-813ab5763567','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611'),('fd85a21a-695b-467a-82fd-7ea25796e5ef','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('fe136326-702d-4e04-b5eb-c72b1e20e177','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('feb66544-1496-43de-be94-1faeaf48ad3e','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('ff21a27e-970a-45d2-a2d4-60998bc9f6c6','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('ff70b347-b597-4256-919b-28276d074545','3c786fc4-1f43-4594-8cd6-9274d827170b','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:23:43.636','2025-05-30 11:23:43.636'),('ffa44a4c-1e19-4595-aa7b-9ecefcb7fd85','027edaf5-e52e-4559-8cc5-aa8a7fcb0435','AVAILABLE',NULL,NULL,NULL,'2025-05-30 11:24:21.611','2025-05-30 11:24:21.611');
/*!40000 ALTER TABLE `tickets` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `transaction`
--

DROP TABLE IF EXISTS `transaction`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `transaction` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` double NOT NULL,
  `type` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `note` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `senderId` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `recipientId` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  `processedById` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `processedAt` datetime(3) DEFAULT NULL,
  `paymentMethod` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `receiptImage` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ledgerId` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `payCodeId` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `transaction_senderId_fkey` (`senderId`),
  KEY `transaction_recipientId_fkey` (`recipientId`),
  KEY `transaction_processedById_fkey` (`processedById`),
  KEY `transaction_ledgerId_fkey` (`ledgerId`),
  KEY `transaction_payCodeId_fkey` (`payCodeId`),
  CONSTRAINT `transaction_ledgerId_fkey` FOREIGN KEY (`ledgerId`) REFERENCES `ledger` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `transaction_payCodeId_fkey` FOREIGN KEY (`payCodeId`) REFERENCES `paycode` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `transaction_processedById_fkey` FOREIGN KEY (`processedById`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `transaction_recipientId_fkey` FOREIGN KEY (`recipientId`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `transaction_senderId_fkey` FOREIGN KEY (`senderId`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `transaction`
--

LOCK TABLES `transaction` WRITE;
/*!40000 ALTER TABLE `transaction` DISABLE KEYS */;
/*!40000 ALTER TABLE `transaction` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `uploaded_image`
--

DROP TABLE IF EXISTS `uploaded_image`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `uploaded_image` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `filename` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `path` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `url` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `mimeType` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `size` int NOT NULL,
  `entityId` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `entityType` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uploaded_image_entityType_entityId_filename_key` (`entityType`,`entityId`,`filename`),
  KEY `uploaded_image_entityType_entityId_idx` (`entityType`,`entityId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `uploaded_image`
--

LOCK TABLES `uploaded_image` WRITE;
/*!40000 ALTER TABLE `uploaded_image` DISABLE KEYS */;
INSERT INTO `uploaded_image` VALUES ('cmbap0er10000lhn48xa6vccb','bday-fleshball-final.png','public\\uploads\\news\\1748602887083-bday-fleshball-final.png','/api/images/cmbap0er10000lhn48xa6vccb','image/png',25086,NULL,'news','2025-05-30 11:01:27.086','2025-05-30 11:01:27.090'),('cmbap5uay0000lhg0kyt3rwil','discord-1367854194022813706-51ace8f03245031970bb487a47eb7af0.png','public\\uploads\\avatars\\1750105288521-discord-1367854194022813706-51ace8f03245031970bb487a47eb7af0.png','/api/images/cmbap5uay0000lhg0kyt3rwil','image/png',70437,'68bc5359-331c-4b28-8ff3-a1e5fd2660a3','avatar','2025-05-30 11:05:40.522','2025-06-16 20:21:28.529'),('cmbr25idj0000lhc4mssq47uo','discord-261755488738213898-26572cbd0fd01f8f94ae119ca9f18388.png','public\\uploads\\avatars\\*************-discord-261755488738213898-26572cbd0fd01f8f94ae119ca9f18388.png','/api/images/cmbr25idj0000lhc4mssq47uo','image/png',61496,'3e6244fb-5234-4f31-b32b-e14108f4c3f0','avatar','2025-06-10 21:53:38.887','2025-06-10 21:53:38.891'),('cmbzipjdb0000lhu0prx3cx7i','Bank-Building.jpg','public\\uploads\\news\\*************-Bank-Building.jpg','/api/images/cmbzipjdb0000lhu0prx3cx7i','image/jpeg',348424,NULL,'news','2025-06-16 19:59:16.560','2025-06-16 19:59:16.564'),('cmbzjfzrc0001lhu0u5t0ds20','avatar-*************.jpg','public\\uploads\\avatars\\*************-avatar-*************.jpg','/api/images/cmbzjfzrc0001lhu0u5t0ds20','image/jpeg',4093,NULL,'avatar','2025-06-16 20:19:50.857','2025-06-16 20:19:50.863'),('cmbzjgz820002lhu0gzulqt20','avatar-*************.jpg','public\\uploads\\avatars\\*************-avatar-*************.jpg','/api/images/cmbzjgz820002lhu0gzulqt20','image/jpeg',7002,NULL,'avatar','2025-06-16 20:20:36.818','2025-06-16 20:20:36.823');
/*!40000 ALTER TABLE `uploaded_image` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user`
--

DROP TABLE IF EXISTS `user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `username` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `displayName` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `passwordHash` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `avatar` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '/images/avatars/default.png',
  `balance` double NOT NULL DEFAULT '0',
  `isEmailVerified` tinyint(1) NOT NULL DEFAULT '0',
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  `defaultView` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'dashboard',
  `notifyTransfers` tinyint(1) NOT NULL DEFAULT '1',
  `notifyDeposits` tinyint(1) NOT NULL DEFAULT '1',
  `notifyWithdrawals` tinyint(1) NOT NULL DEFAULT '1',
  `notifyNewsEvents` tinyint(1) NOT NULL DEFAULT '0',
  `discordConnected` tinyint(1) NOT NULL DEFAULT '0',
  `discordId` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `facebookConnected` tinyint(1) NOT NULL DEFAULT '0',
  `facebookId` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `merchantStatus` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT 'none',
  `merchantId` int DEFAULT NULL,
  `merchantSlug` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `hasCreatedAuctions` tinyint(1) NOT NULL DEFAULT '0',
  `auctionCount` int NOT NULL DEFAULT '0',
  `isAdmin` tinyint(1) NOT NULL DEFAULT '0',
  `isEditor` tinyint(1) NOT NULL DEFAULT '0',
  `isBanker` tinyint(1) NOT NULL DEFAULT '0',
  `isChatModerator` tinyint(1) NOT NULL DEFAULT '0',
  `status` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active',
  `notifyAuctions` tinyint(1) NOT NULL DEFAULT '1',
  `notifyChat` tinyint(1) NOT NULL DEFAULT '1',
  `notifyAdmin` tinyint(1) NOT NULL DEFAULT '1',
  `isVolunteer` tinyint(1) NOT NULL DEFAULT '0',
  `isLeadManager` tinyint(1) NOT NULL DEFAULT '0',
  `isVolunteerCoordinator` tinyint(1) NOT NULL DEFAULT '0',
  `leadManagerCategoryId` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `isSalesManager` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_email_key` (`email`),
  UNIQUE KEY `user_username_key` (`username`),
  UNIQUE KEY `user_leadManagerCategoryId_key` (`leadManagerCategoryId`),
  CONSTRAINT `user_leadManagerCategoryId_fkey` FOREIGN KEY (`leadManagerCategoryId`) REFERENCES `volunteer_categories` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user`
--

LOCK TABLES `user` WRITE;
/*!40000 ALTER TABLE `user` DISABLE KEYS */;
INSERT INTO `user` VALUES ('3e6244fb-5234-4f31-b32b-e14108f4c3f0','snatchyocash420','snatchyocash420','<EMAIL>','','/api/images/cmbzjfzrc0001lhu0u5t0ds20',0,0,'2025-06-10 21:53:38.412','2025-06-16 20:19:50.878','dashboard',1,1,1,0,1,'261755488738213898',0,NULL,'none',NULL,NULL,0,0,0,0,0,0,'active',1,1,1,0,0,0,NULL,0),('68bc5359-331c-4b28-8ff3-a1e5fd2660a3','theweekafter','theweekafter','<EMAIL>','','/api/images/cmbap5uay0000lhg0kyt3rwil',0,0,'2025-05-30 11:05:40.077','2025-06-16 20:21:28.532','dashboard',1,1,1,0,1,'1367854194022813706',0,NULL,'none',NULL,NULL,0,0,0,0,0,0,'active',1,1,1,0,1,0,'b9d8e085-b6b1-4c92-95f1-79401e2d5a59',0),('c1b4d722-547a-41a1-bf0c-45fbbacc9a92','ChesterBait','ChesterBait','<EMAIL>','$2b$10$cqY6g1kYtfwYZKPjsnWBLudBfIGpDqyU1yp26l8L5LcKTgNvq8Hhm','/api/images/cmbzjgz820002lhu0gzulqt20',1000,1,'2025-05-30 10:30:09.348','2025-06-16 20:20:36.833','dashboard',1,1,1,1,0,NULL,0,NULL,'none',NULL,NULL,0,0,1,1,1,1,'active',1,1,1,0,0,1,NULL,1);
/*!40000 ALTER TABLE `user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_credential`
--

DROP TABLE IF EXISTS `user_credential`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_credential` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `userId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `identifier` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `passwordHash` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `accessToken` text COLLATE utf8mb4_unicode_ci,
  `refreshToken` text COLLATE utf8mb4_unicode_ci,
  `tokenExpiry` datetime(3) DEFAULT NULL,
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  `lastUsedAt` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_credential_type_identifier_key` (`type`,`identifier`),
  KEY `user_credential_userId_idx` (`userId`),
  CONSTRAINT `user_credential_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_credential`
--

LOCK TABLES `user_credential` WRITE;
/*!40000 ALTER TABLE `user_credential` DISABLE KEYS */;
INSERT INTO `user_credential` VALUES ('0566e421-b55a-48eb-8f5d-b5f0ddbb1e2f','c1b4d722-547a-41a1-bf0c-45fbbacc9a92','email','<EMAIL>','$2b$10$cqY6g1kYtfwYZKPjsnWBLudBfIGpDqyU1yp26l8L5LcKTgNvq8Hhm',NULL,NULL,NULL,'2025-05-30 10:33:16.444','2025-06-20 01:12:31.382','2025-06-20 01:12:31.381'),('134b73f8-9c8e-48f6-adda-6961d1257cab','3e6244fb-5234-4f31-b32b-e14108f4c3f0','email','<EMAIL>',NULL,NULL,NULL,NULL,'2025-06-10 21:53:38.417','2025-06-10 21:53:38.417','2025-06-10 21:53:38.416'),('4edf4d66-521f-409c-84b1-874e085bea08','68bc5359-331c-4b28-8ff3-a1e5fd2660a3','email','<EMAIL>',NULL,NULL,NULL,NULL,'2025-05-30 11:05:40.083','2025-05-30 11:05:40.083','2025-05-30 11:05:40.075'),('80cb940f-9e1d-4494-bc60-a49e1b35eebe','68bc5359-331c-4b28-8ff3-a1e5fd2660a3','discord','1367854194022813706',NULL,'MTM2Nzg0NjQ3NzU2MDU0OTQ0OA.k9xiGOSejHqXaCJ1GKgkDdqLFMk0Qz','oIykhJmAG8QJY8yCEqA9GBkHEKdZ4M','2025-06-23 20:21:28.062','2025-05-30 11:05:40.077','2025-06-16 20:21:28.062','2025-06-16 20:21:28.062'),('96404fdf-493c-49d9-b06c-8fc97f3ad3e2','3e6244fb-5234-4f31-b32b-e14108f4c3f0','discord','261755488738213898',NULL,'MTM2Nzg0NjQ3NzU2MDU0OTQ0OA.4SNtsssPTiujiAb0SuLFYl8s7N5i0h','BTuEJ9VHPDcz5bcDmzaMA5BRMjOht3','2025-06-17 21:53:38.411','2025-06-10 21:53:38.412','2025-06-10 21:53:38.412','2025-06-10 21:53:38.411');
/*!40000 ALTER TABLE `user_credential` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_state`
--

DROP TABLE IF EXISTS `user_state`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_state` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `userId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `lastNewsSync` datetime(3) DEFAULT NULL,
  `lastCategorySync` datetime(3) DEFAULT NULL,
  `betaFeaturesEnabled` tinyint(1) NOT NULL DEFAULT '0',
  `darkModeEnabled` tinyint(1) NOT NULL DEFAULT '1',
  `compactViewEnabled` tinyint(1) NOT NULL DEFAULT '0',
  `globalCacheVersion` int NOT NULL DEFAULT '1',
  `lastActive` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `deviceInfo` text COLLATE utf8mb4_unicode_ci,
  `pageViews` int NOT NULL DEFAULT '0',
  `articleReads` int NOT NULL DEFAULT '0',
  `customState` json DEFAULT NULL,
  `hasAccessToAuctions` tinyint(1) NOT NULL DEFAULT '1',
  `hasAccessToMerchants` tinyint(1) NOT NULL DEFAULT '1',
  `hasAccessToChat` tinyint(1) NOT NULL DEFAULT '1',
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_state_userId_key` (`userId`),
  KEY `user_state_userId_idx` (`userId`),
  CONSTRAINT `user_state_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_state`
--

LOCK TABLES `user_state` WRITE;
/*!40000 ALTER TABLE `user_state` DISABLE KEYS */;
INSERT INTO `user_state` VALUES ('19315465-2206-4344-9618-b84d9a116692','68bc5359-331c-4b28-8ff3-a1e5fd2660a3','2025-06-17 12:20:37.928','2025-06-17 12:20:37.926',0,1,0,1,'2025-06-17 12:20:45.675','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',29,0,NULL,1,1,1,'2025-05-30 11:05:40.718','2025-06-17 12:20:45.681'),('a833d111-481d-4b6c-b421-8f23d3d8c75f','3e6244fb-5234-4f31-b32b-e14108f4c3f0','2025-06-16 20:30:04.300','2025-06-16 20:30:04.297',0,1,0,1,'2025-06-16 20:30:20.690','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',22,2,'{\"readArticles\": [\"99e06921-c999-4d4e-b5c9-a89761376809\", \"bb1ed53d-5c1b-467c-b7e2-a529d97d17d6\"]}',1,1,1,'2025-06-10 21:53:39.102','2025-06-16 20:30:20.698'),('f9d47598-b827-499a-ae16-1a012cb287c7','c1b4d722-547a-41a1-bf0c-45fbbacc9a92','2025-06-16 20:00:30.982','2025-06-16 20:00:30.981',0,1,0,1,'2025-06-22 00:51:59.444','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',112,2,'{\"readArticles\": [\"99e06921-c999-4d4e-b5c9-a89761376809\", \"bb1ed53d-5c1b-467c-b7e2-a529d97d17d6\"]}',1,1,1,'2025-05-30 10:33:16.479','2025-06-22 00:51:56.714');
/*!40000 ALTER TABLE `user_state` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `verification_code`
--

DROP TABLE IF EXISTS `verification_code`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `verification_code` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `userId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiresAt` datetime(3) NOT NULL,
  `verified` tinyint(1) NOT NULL DEFAULT '0',
  `attempts` int NOT NULL DEFAULT '0',
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `verification_code_userId_type_key` (`userId`,`type`),
  KEY `verification_code_userId_idx` (`userId`),
  KEY `verification_code_code_idx` (`code`),
  CONSTRAINT `verification_code_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `verification_code`
--

LOCK TABLES `verification_code` WRITE;
/*!40000 ALTER TABLE `verification_code` DISABLE KEYS */;
/*!40000 ALTER TABLE `verification_code` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `volunteer_assignments`
--

DROP TABLE IF EXISTS `volunteer_assignments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `volunteer_assignments` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `userId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `shiftId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `notes` text COLLATE utf8mb4_unicode_ci,
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  `metadata` json DEFAULT NULL,
  `slotId` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `volunteer_assignments_userId_shiftId_key` (`userId`,`shiftId`),
  UNIQUE KEY `volunteer_assignments_slotId_key` (`slotId`),
  KEY `volunteer_assignments_userId_idx` (`userId`),
  KEY `volunteer_assignments_shiftId_idx` (`shiftId`),
  KEY `volunteer_assignments_status_idx` (`status`),
  KEY `volunteer_assignments_slotId_idx` (`slotId`),
  CONSTRAINT `volunteer_assignments_shiftId_fkey` FOREIGN KEY (`shiftId`) REFERENCES `volunteer_shifts` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `volunteer_assignments_slotId_fkey` FOREIGN KEY (`slotId`) REFERENCES `volunteer_slots` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `volunteer_assignments_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `volunteer_assignments`
--

LOCK TABLES `volunteer_assignments` WRITE;
/*!40000 ALTER TABLE `volunteer_assignments` DISABLE KEYS */;
/*!40000 ALTER TABLE `volunteer_assignments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `volunteer_categories`
--

DROP TABLE IF EXISTS `volunteer_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `volunteer_categories` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `payRate` double DEFAULT NULL,
  `eventId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  `leadManagerId` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `volunteer_categories_leadManagerId_key` (`leadManagerId`),
  KEY `volunteer_categories_eventId_idx` (`eventId`),
  CONSTRAINT `volunteer_categories_eventId_fkey` FOREIGN KEY (`eventId`) REFERENCES `events` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `volunteer_categories`
--

LOCK TABLES `volunteer_categories` WRITE;
/*!40000 ALTER TABLE `volunteer_categories` DISABLE KEYS */;
INSERT INTO `volunteer_categories` VALUES ('b9d8e085-b6b1-4c92-95f1-79401e2d5a59','cleanup','Clean up the ****',50,'e40ca619-7d39-4767-87e3-7e83edfd97e1','2025-05-30 11:06:41.159','2025-05-30 11:06:41.159','68bc5359-331c-4b28-8ff3-a1e5fd2660a3');
/*!40000 ALTER TABLE `volunteer_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `volunteer_hours`
--

DROP TABLE IF EXISTS `volunteer_hours`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `volunteer_hours` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `assignmentId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `userId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `hoursWorked` double NOT NULL,
  `paymentAmount` double NOT NULL,
  `paymentStatus` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `verifiedById` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `verifiedAt` datetime(3) DEFAULT NULL,
  `transactionId` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `volunteer_hours_assignmentId_key` (`assignmentId`),
  KEY `volunteer_hours_userId_idx` (`userId`),
  KEY `volunteer_hours_assignmentId_idx` (`assignmentId`),
  KEY `volunteer_hours_paymentStatus_idx` (`paymentStatus`),
  KEY `volunteer_hours_verifiedById_fkey` (`verifiedById`),
  KEY `volunteer_hours_transactionId_fkey` (`transactionId`),
  CONSTRAINT `volunteer_hours_assignmentId_fkey` FOREIGN KEY (`assignmentId`) REFERENCES `volunteer_assignments` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `volunteer_hours_transactionId_fkey` FOREIGN KEY (`transactionId`) REFERENCES `transaction` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `volunteer_hours_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `volunteer_hours_verifiedById_fkey` FOREIGN KEY (`verifiedById`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `volunteer_hours`
--

LOCK TABLES `volunteer_hours` WRITE;
/*!40000 ALTER TABLE `volunteer_hours` DISABLE KEYS */;
/*!40000 ALTER TABLE `volunteer_hours` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `volunteer_notification_preferences`
--

DROP TABLE IF EXISTS `volunteer_notification_preferences`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `volunteer_notification_preferences` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `assignmentId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `emailNotification` tinyint(1) NOT NULL DEFAULT '1',
  `websiteNotification` tinyint(1) NOT NULL DEFAULT '1',
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `volunteer_notification_preferences_assignmentId_key` (`assignmentId`),
  CONSTRAINT `volunteer_notification_preferences_assignmentId_fkey` FOREIGN KEY (`assignmentId`) REFERENCES `volunteer_assignments` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `volunteer_notification_preferences`
--

LOCK TABLES `volunteer_notification_preferences` WRITE;
/*!40000 ALTER TABLE `volunteer_notification_preferences` DISABLE KEYS */;
/*!40000 ALTER TABLE `volunteer_notification_preferences` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `volunteer_shifts`
--

DROP TABLE IF EXISTS `volunteer_shifts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `volunteer_shifts` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `startTime` datetime(3) NOT NULL,
  `endTime` datetime(3) NOT NULL,
  `location` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `maxVolunteers` int NOT NULL DEFAULT '1',
  `eventId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `categoryId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `isAutomated` tinyint(1) NOT NULL DEFAULT '0',
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `volunteer_shifts_eventId_idx` (`eventId`),
  KEY `volunteer_shifts_categoryId_idx` (`categoryId`),
  KEY `volunteer_shifts_startTime_idx` (`startTime`),
  CONSTRAINT `volunteer_shifts_categoryId_fkey` FOREIGN KEY (`categoryId`) REFERENCES `volunteer_categories` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `volunteer_shifts_eventId_fkey` FOREIGN KEY (`eventId`) REFERENCES `events` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `volunteer_shifts`
--

LOCK TABLES `volunteer_shifts` WRITE;
/*!40000 ALTER TABLE `volunteer_shifts` DISABLE KEYS */;
INSERT INTO `volunteer_shifts` VALUES ('bce15ba3-5c8b-44e8-9e68-8fd28e677b67','biffy clean up (8:00 AM - 10:00 AM)','Sprain out the Biffy','2025-05-30 15:00:00.000','2025-05-30 17:00:00.000','The whole site',4,'e40ca619-7d39-4767-87e3-7e83edfd97e1','b9d8e085-b6b1-4c92-95f1-79401e2d5a59',0,'2025-05-30 11:08:31.770','2025-05-30 11:08:31.770'),('ffc07060-ef01-4f31-9e91-0a21d1e0afbb','biffy clean up (10:00 AM - 12:00 PM)','Sprain out the Biffy','2025-05-30 17:00:00.000','2025-05-30 19:00:00.000','The whole site',4,'e40ca619-7d39-4767-87e3-7e83edfd97e1','b9d8e085-b6b1-4c92-95f1-79401e2d5a59',0,'2025-05-30 11:08:31.772','2025-05-30 11:08:31.772');
/*!40000 ALTER TABLE `volunteer_shifts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `volunteer_slot_holds`
--

DROP TABLE IF EXISTS `volunteer_slot_holds`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `volunteer_slot_holds` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `userId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiresAt` datetime(3) NOT NULL,
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `volunteer_slot_holds_userId_idx` (`userId`),
  KEY `volunteer_slot_holds_expiresAt_idx` (`expiresAt`),
  CONSTRAINT `volunteer_slot_holds_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `volunteer_slot_holds`
--

LOCK TABLES `volunteer_slot_holds` WRITE;
/*!40000 ALTER TABLE `volunteer_slot_holds` DISABLE KEYS */;
/*!40000 ALTER TABLE `volunteer_slot_holds` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `volunteer_slots`
--

DROP TABLE IF EXISTS `volunteer_slots`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `volunteer_slots` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `shiftId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('AVAILABLE','HELD','SOLD','CANCELLED') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'AVAILABLE',
  `holdId` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `assignmentId` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `volunteer_slots_assignmentId_key` (`assignmentId`),
  KEY `volunteer_slots_shiftId_idx` (`shiftId`),
  KEY `volunteer_slots_status_idx` (`status`),
  KEY `volunteer_slots_holdId_idx` (`holdId`),
  KEY `volunteer_slots_assignmentId_idx` (`assignmentId`),
  CONSTRAINT `volunteer_slots_holdId_fkey` FOREIGN KEY (`holdId`) REFERENCES `volunteer_slot_holds` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `volunteer_slots_shiftId_fkey` FOREIGN KEY (`shiftId`) REFERENCES `volunteer_shifts` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `volunteer_slots`
--

LOCK TABLES `volunteer_slots` WRITE;
/*!40000 ALTER TABLE `volunteer_slots` DISABLE KEYS */;
INSERT INTO `volunteer_slots` VALUES ('2613b164-c595-4a70-972c-b8e2e5a44560','ffc07060-ef01-4f31-9e91-0a21d1e0afbb','AVAILABLE',NULL,NULL,'2025-05-30 11:08:31.773','2025-05-30 11:08:31.773'),('2e0827ce-9d10-481c-9d11-49d48be32081','bce15ba3-5c8b-44e8-9e68-8fd28e677b67','AVAILABLE',NULL,NULL,'2025-05-30 11:08:31.771','2025-05-30 11:08:31.771'),('35df78f3-e57f-462b-8b17-d7f64aafd790','bce15ba3-5c8b-44e8-9e68-8fd28e677b67','AVAILABLE',NULL,NULL,'2025-05-30 11:08:31.771','2025-05-30 11:08:31.771'),('50905ed0-9554-4397-80b9-300893ee5e3d','ffc07060-ef01-4f31-9e91-0a21d1e0afbb','AVAILABLE',NULL,NULL,'2025-05-30 11:08:31.773','2025-05-30 11:08:31.773'),('553490c4-7535-43d1-9193-cceaa57c53b2','ffc07060-ef01-4f31-9e91-0a21d1e0afbb','AVAILABLE',NULL,NULL,'2025-05-30 11:08:31.773','2025-05-30 11:08:31.773'),('64b62080-e160-4b59-b653-24d907d9ccc7','ffc07060-ef01-4f31-9e91-0a21d1e0afbb','AVAILABLE',NULL,NULL,'2025-05-30 11:08:31.773','2025-05-30 11:08:31.773'),('daa23f1c-6542-4932-84ee-bbb384eaa131','bce15ba3-5c8b-44e8-9e68-8fd28e677b67','AVAILABLE',NULL,NULL,'2025-05-30 11:08:31.771','2025-05-30 11:08:31.771'),('de5ee392-efa1-43d6-9ae4-83132450f3cc','bce15ba3-5c8b-44e8-9e68-8fd28e677b67','AVAILABLE',NULL,NULL,'2025-05-30 11:08:31.771','2025-05-30 11:08:31.771');
/*!40000 ALTER TABLE `volunteer_slots` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping events for database 'bank_of_styx'
--

--
-- Dumping routines for database 'bank_of_styx'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-28 22:54:16
