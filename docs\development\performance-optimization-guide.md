# Bank of Styx - Performance Optimization Guide

## Overview
This document outlines the key strategies, patterns, and tools used within the Bank of Styx application to ensure high performance, scalability, and a responsive user experience. It covers frontend, backend, database, and real-time system optimizations.

## 1. Frontend Performance (React & Assets)

### React Performance Patterns
The codebase leverages several React patterns to minimize re-renders and optimize component performance.

#### Memoization with `useCallback` and `useMemo`
- **`useCallback`**: Used for functions passed down to child components as props. This prevents the child components from re-rendering unnecessarily because the function reference remains stable between renders.
- **`useMemo`**: Used to memoize the result of expensive calculations. The calculation is only re-run when one of its dependencies changes.

#### State Management
- **TanStack Query**: Manages server state, providing out-of-the-box caching, request de-duplication, and background refetching. This reduces the number of redundant API calls and keeps the UI in sync with the server efficiently.
- **Zustand**: Used for global client state. Its lightweight nature and selector-based subscription model ensure that components only re-render when the specific state they subscribe to changes.

#### Component Architecture
- **Server Components (RSC)**: By default, Next.js App Router uses Server Components. This pattern is used extensively to fetch data and render static content on the server, significantly reducing the amount of JavaScript shipped to the client.
- **Client Components (`"use client"`)**: Used only for components that require interactivity, state, or browser-only APIs (e.g., `useEffect`, `useState`). This is a deliberate choice to keep the client-side footprint as small as possible. The performance test page at `web/apps/main-site/src/app/test/performance/page.tsx` is a good example.

### Asset Optimization

#### Image Processing
- The application uses the **Sharp** library for all image uploads.
- Images are automatically resized, optimized, and converted to modern formats like WebP to reduce file size without sacrificing quality.
- This is handled server-side, ensuring that clients download the smallest possible image assets. See the `upload-system.md` context file for more details.

#### Code Splitting
- The Next.js framework automatically performs code splitting on a per-page basis. Each route only loads the JavaScript necessary for that page, leading to faster initial page loads.

#### Shared UI Library
- All common UI elements are part of the shared UI package at `web/packages/ui`. This promotes reusability, consistency, and reduces the overall bundle size by preventing duplicate component code.

---

## 2. Database Query Optimization
Database performance is critical. The project employs several techniques to ensure fast and efficient queries.

### Indexing
- Database indexes are applied to frequently queried columns, especially foreign keys and columns used in `WHERE` clauses or for sorting.
- **Example**: The `Notification` model is indexed on `userId` and `createdAt` to speed up fetching a user's notifications in chronological order.

```sql
-- From notification-system.md
-- Indexed by userId, createdAt DESC
```

### Projections (Selective Field Loading)
- Prisma's `select` and `include` options are used to fetch only the required fields from the database. This minimizes data transfer between the database and the server and reduces memory usage.

```typescript
// Example of fetching only specific fields
const userPreferences = await prisma.user.findUnique({
  where: { id: userId },
  select: {
    notifyTransfers: true,
    notifyDeposits: true,
    // ... other preference fields
  }
});
```

### Pagination
- For large datasets like transaction histories or notification lists, pagination is always implemented using Prisma's `take` (LIMIT) and `skip` (OFFSET) or cursor-based pagination. This prevents loading thousands of records at once.

### Advanced Query Analysis & Logging
The Prisma client in `web/apps/main-site/src/lib/prisma.ts` is enhanced with a powerful custom logger for development.

- **Detailed Logging**: It logs every query with syntax highlighting, duration, parameters, and memory usage.
- **Performance Indicators**: Queries are automatically flagged as "⚡ Fast", "🐢 Slow", or "⏱️ Normal" based on their duration relative to the average.
- **`QueryBuffer` Class**: A sophisticated in-memory buffer that analyzes queries over a 60-second window, providing stats on:
  - Queries per second (QPS)
  - Latency percentiles (p50, p95, p99)
  - Memory growth rate
  - Query "hotspots" (frequently run, slow queries)

This tool is invaluable for identifying and debugging performance bottlenecks at the database level during development.

---

## 3. Real-Time System (SSE) Performance
The Server-Sent Events (SSE) system is optimized for efficiency and scalability.

### Connection Management
- **Centralized Store**: A `connectionStore` manages all active client connections, providing a single source of truth for real-time delivery.
- **One Connection Per User**: The system is designed to maintain only one SSE connection per active user, regardless of how many tabs or devices they have open. This significantly conserves server resources.
- **Heartbeat & Cleanup**: A heartbeat mechanism periodically checks connection health and automatically cleans up stale or disconnected clients.

### Efficient Message Delivery
- **Targeted Broadcasting**: Notifications are broadcast to specific users, multiple users, or roles, avoiding unnecessary data transmission to unaffected clients.
- **Batching**: Where possible, multiple notifications for a single user can be batched into one SSE message to reduce overhead.
- **Persistent Fallback**: For offline users, notifications are stored in the database. This prevents failed delivery attempts and ensures users receive all notifications when they next log in, without keeping connections open indefinitely.

---

## 4. API & Server-Side Optimization

### Caching Strategies
- **Server-Side Caching**: For frequently accessed data that doesn't change often (e.g., system-wide settings, public statistics), server-side caching can be implemented to reduce database load.
- **Client-Side Caching**: Handled by TanStack Query, which caches API responses on the client to avoid re-fetching data on subsequent requests or navigation.

### Asynchronous Operations
- The entire backend leverages `async/await` for non-blocking I/O operations. This is fundamental to Node.js performance, allowing the server to handle many concurrent requests efficiently without getting blocked by database queries or other external service calls.

### Rate Limiting
- Sensitive endpoints, particularly those related to authentication and financial transactions, should have rate limiting implemented to prevent abuse and ensure service stability.

---

## 5. Monitoring and Profiling
Continuous monitoring is key to maintaining performance.

### In-App Performance Page
- The page at `/test/performance` provides a built-in interface for developers to benchmark the performance of key API endpoints and the SSE connection/delivery lifecycle.
- This allows for quick, repeatable tests to measure the impact of code changes in both development and production environments.

### Database Query Logging
- The custom Prisma logger in `web/apps/main-site/src/lib/prisma.ts` is the primary tool for database profiling in development. It provides immediate feedback on query performance.

### Standard Tooling
- **React DevTools Profiler**: Use this to analyze component render times and identify performance bottlenecks in the frontend.
- **Browser DevTools**: The "Performance" and "Lighthouse" tabs in Chrome DevTools are essential for analyzing page load times, rendering performance, and overall user experience.

---

## Quick Reference: Performance Checklist

When developing a new feature, consider the following:

- **[ ] Frontend**:
  - Is state managed efficiently (Zustand/TanStack Query)?
  - Are `useCallback`/`useMemo` used for expensive operations or props?
  - Is `"use client"` only used when absolutely necessary?
  - Are images being processed through the upload system?

- **[ ] Backend/API**:
  - Are API responses lean, returning only necessary data?
  - Can any data be cached on the server?

- **[ ] Database**:
  - Do new database columns need an index?
  - Are you using `select` to limit the data returned?
  - Is pagination implemented for lists of data?
  - Have you checked the development console for slow queries?