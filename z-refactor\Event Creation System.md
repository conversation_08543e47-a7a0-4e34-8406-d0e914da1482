● Event and Ticket Creation System Report

  Event Creation System

  Location: /api/admin/events (POST) - web/apps/main-site/src/app/api/admin/events/route.ts:97

  Admin-Only Access: Events can only be created by users with admin role permissions.

  Event Creation Process:
  1. Authentication: Validates admin role using userHasRole(req, "admin")
  2. Data Validation: Requires name, description, startDate, endDate, and categoryId
  3. Date Validation: Ensures endDate is after startDate
  4. Category Validation: Verifies the event category exists
  5. Database Creation: Creates event record with status defaulting to "draft"

  Event Frontend: Admin interface at /admin/events/new/page.tsx provides comprehensive form with:
  - Event details (name, description, dates, location)
  - Virtual/physical event toggle
  - Image upload capability
  - Category selection
  - Capacity limits
  - Status control (draft/published)

  Ticket System Architecture

  Important Discovery: This system uses product-based tickets, not traditional event tickets.

  Ticket Types:
  1. Product Tickets: Individual tickets for purchasable products (handled in lib/ticket-system.ts)
  2. Volunteer Slots: Tickets for volunteer shift participation

  Product Ticket System:
  - Generation: generateTicketsForProduct() creates individual ticket records for each product unit
  - Status Flow: AVAILABLE → HELD (15min) → SOLD/CANCELLED
  - Hold System: 15-minute cart holds prevent overselling
  - Inventory Sync: Tickets automatically sync with product inventory changes

  Event-Ticket Relationship:
  - Events themselves don't directly create tickets
  - Events can have associated products that generate tickets
  - Volunteer shifts within events create volunteer slots (similar to tickets)

  Key Integration Points

  Event → Product → Tickets:
  1. Event is created by admin
  2. Products can be associated with events (separate system)
  3. Products automatically generate individual ticket records
  4. Users purchase product tickets through cart/checkout system

  Event → Volunteer System:
  1. Event is created with categories
  2. Volunteer shifts are created within categories
  3. Shifts auto-generate volunteer slots (ticket-like system)
  4. Users sign up for shifts, consuming slots

  File References

  - Event creation: src/app/api/admin/events/route.ts:97-191
  - Event form: src/app/admin/events/new/page.tsx:104-150
  - Ticket system: src/lib/ticket-system.ts:11-25
  - Volunteer system: context/event-volunteer-system.md:64-80



## ENHANCEMENT PROPOSAL: Event-Based Capacity System

### Current Problem
The existing system has events with unused capacity fields and products that generate individual tickets independently, with no connection between event capacity limits and actual product sales across multiple ticket types.

### Proposed Solution
Implement a **shared event capacity management system** where multiple product types (Admin tickets, Merchant tickets, General admission, Kids tickets) associated with an event draw from a common capacity pool rather than individual product inventory limits.

### Key Requirements
1. **Shared Event Capacity Pool**: Event capacity becomes the master limit for all associated products
2. **Dynamic Capacity Management**: Both SOLD and HELD (cart) tickets reduce event's available capacity
3. **No Individual Ticket Generation**: Skip `generateTicketsForProduct()` for event-associated products
4. **Cross-Product Capacity Sharing**: All product types draw from the same event capacity pool
5. **Hold System Integration**: Cart holds must check and reserve against event capacity during 15-minute hold period

### Implementation Details
**Comprehensive change report created**: `Event-Based-Capacity-System-Changes.md`

This enhancement will prevent overselling across multiple ticket types while maintaining the existing hold system's duplicate prevention through atomic database transactions.