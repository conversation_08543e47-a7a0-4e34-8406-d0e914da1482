# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Critical Context Documentation

### Detailed System Documentation
For comprehensive understanding of core systems, refer to the `context/` folder which contains:
- **authentication.md**: JWT auth flow, Discord OAuth, role-based permissions
- **banking-system.md**: Transaction processing, pay codes, balance management
- **real-time-sse.md**: Server-Sent Events architecture and connection management
- **upload-system.md**: Multi-type file uploads with image processing
- **event-volunteer-system.md**: Event management and volunteer scheduling
- **product-shopping-system.md**: Product management and cart hold system
- **support-ticket-system.md**: Support ticketing with email integration
- **news-content-management-system.md**: Article management with ReactQuill
- **notification-system.md**: Multi-channel notifications with user preferences
- **color-theme-ui-elements.md**: Design system and UI consistency

### Permission Matrix
- **Users**: Can modify own data, create own tickets, view public content
- **Moderators**: Can manage events, volunteer shifts, moderate content
- **Admins**: Full system access, user management, financial operations
- **Super Admins**: System configuration, role assignment, critical operations

### Database Transaction Patterns
- Use Prisma transactions for atomic operations (transfers, order processing)
- Implement optimistic locking for concurrent access (cart holds, balance updates)
- Transaction isolation for financial operations to prevent race conditions
- Audit trails for all critical data changes (transactions, role changes)

### Error Handling Patterns
- Standardized API error responses with consistent structure
- Client-side error boundaries for graceful failure handling
- Comprehensive logging with appropriate log levels
- User-friendly error messages while maintaining security

### Performance Considerations
- Implement pagination for large data sets (transactions, notifications)
- Use database indexes on frequently queried fields (userId, createdAt)
- SSE connection limits and cleanup for resource management
- Image optimization and caching for upload system

### Security Best Practices
- JWT token validation on all protected endpoints
- Input sanitization and validation for all user inputs
- Rate limiting on sensitive endpoints (auth, payments)
- CORS configuration for frontend-backend communication
- Environment-based configuration for sensitive data

### Critical Implementation Patterns (Lessons from Phase 4)
**ALWAYS verify these patterns before implementing new features:**

#### Authentication in API Routes
- **CORRECT**: `const user = await getCurrentUser(request);` - Always pass request parameter
- **WRONG**: `const user = await getCurrentUser();` - Missing required parameter
- **Pattern**: Server-side auth functions require request context for token extraction

#### Prisma Import Convention
- **CORRECT**: `import prisma from "@/lib/prisma";` - Default import
- **WRONG**: `import { prisma } from "@/lib/prisma";` - Named import (doesn't exist)
- **Pattern**: Check existing imports in similar files before implementation

#### Database ID Handling
- **CORRECT**: `const id = params.id;` - UUIDs are strings, use directly
- **WRONG**: `const id = parseInt(params.id);` - Don't parse UUID strings as numbers
- **Pattern**: Check schema.prisma for actual field types before processing

#### Prisma Field Naming
- **CORRECT**: `submittedById`, `reviewedById` - Prisma auto-generates "Id" suffix for relations
- **WRONG**: `submittedBy`, `reviewedBy` - These are the relation names, not field names
- **Pattern**: Relation fields end with "Id", relation objects use base name

#### Client vs Server Authentication
- **CORRECT (Client)**: `fetch("/api/auth/me")` - Use API endpoints in React components
- **WRONG (Client)**: `getCurrentUser()` - Server utilities don't work in client components
- **Pattern**: Server functions stay on server, client uses API calls

#### Shared Types Architecture
- **CORRECT**: Create shared `types.ts` file for reusable interfaces
- **WRONG**: Duplicate interface definitions across multiple files
- **Pattern**: Plan type architecture before implementation, avoid export conflicts

### UI Component Import Patterns
- **Shared UI Components**: Always import from `@bank-of-styx/ui` package alias
- **Examples**: `import { Modal, Button, Input } from "@bank-of-styx/ui"`
- **Never use**: `@ui/modal` or direct paths - use the proper package alias
- **Available Components**: Modal, Button, Input, Card, Pagination, Spinner, etc.

### UI Component Props Reference
- **Button Component**:
  - Valid `variant` values: "primary", "secondary", "accent", "outline", "ghost"
  - Valid `size` values: "sm", "md", "lg"
  - **Note**: "danger" variant does not exist - use `variant="outline"` with custom red styling classes
- **Modal Component**:
  - Valid `size` values: "sm", "md", "lg", "xl", "full"
  - **Note**: "large" is not valid - use "lg" instead

## Common Development Commands

### Development Server
```bash
# Start development server (runs from root)
pnpm dev

# Start development server with specific host (as configured)
pnpm dev:vscode
```

### Building and Testing
```bash
# Build all packages
pnpm build

# Lint all packages
pnpm lint

# Format code with Prettier
pnpm format

# Build production version
cd web && pnpm build
```

### Database Operations
```bash
# Run database migrations
cd web/apps/main-site && pnpm prisma migrate dev

# Apply migrations to database (after creating migration files)
cd web/apps/main-site && npx prisma migrate deploy

# Generate Prisma client after schema changes
cd web/apps/main-site && npx prisma generate

# Check migration status
cd web/apps/main-site && npx prisma migrate status

# Seed the database
pnpm prisma:seed

# Open Prisma Studio
pnpm prisma:studio
```

### **IMPORTANT WORKFLOW REMINDER**
**After creating Prisma migration files, ALWAYS apply them immediately using:**
1. `npx prisma migrate deploy` - Apply migrations to database
2. `npx prisma generate` - Update Prisma client with schema changes
3. `npx prisma migrate status` - Verify migrations were applied successfully

This ensures the database and Prisma Studio stay in sync with schema changes.

### UI Package Development
```bash
# Build shared UI package
pnpm ui:build

# Develop shared UI package
pnpm ui:dev
```

## Architecture Overview

### Monorepo Structure
This is a PNPM workspace monorepo with:
- **Root**: Contains workspace configuration and shared scripts
- **web/**: Main monorepo containing all applications and packages
- **web/apps/main-site/**: Primary Next.js application with App Router
- **web/apps/api/**: Standalone API modules (auth endpoints)
- **web/packages/ui/**: Shared UI components library
- **web/packages/config/**: Shared configuration files

### Key Technologies
- **Framework**: Next.js 13.4.2 with App Router
- **Database**: MySQL 8.0 with Prisma ORM 6.6.0
- **State Management**: TanStack Query 5.17.19 + Zustand 4.3.8
- **Styling**: TailwindCSS 3.3.2
- **Authentication**: JWT + Discord OAuth
- **Real-time**: Server-Sent Events (SSE)
- **Payments**: Stripe integration
- **Package Manager**: PNPM 8.6.0

### Core Systems
1. **Banking System**: Comprehensive banking with transactions, pay codes, real-time balance updates
2. **Authentication**: Multi-method auth (email/password, Discord OAuth) with JWT sessions
3. **Real-time Updates**: SSE-based notifications and live data updates
4. **News System**: Article management with categories and rich text editing
5. **Event Management**: Event creation, categories, and calendar integration  
6. **Volunteer System**: Shift management, categories, hour tracking, payments
7. **Shopping System**: Product management, cart with hold system, Stripe checkout
8. **Support Tickets**: Priority-based ticket system with assignment and notifications
9. **Ticket Hold System**: 15-minute holds with auto-expiration to prevent overselling

### Database Architecture
- Uses Prisma ORM with MySQL
- Comprehensive migration history in `web/apps/main-site/prisma/migrations/`
- Key models: User, Transaction, News, Event, Volunteer, Product, Ticket, Order
- Database seeding via `prisma/seed.js`

### File Organization
- **API Routes**: `src/app/api/` (Next.js App Router structure)
- **Pages**: `src/app/` with nested route folders
- **Components**: `src/components/` organized by feature area
- **Services**: `src/services/` for API service functions
- **Hooks**: `src/hooks/` for custom React hooks
- **Utils**: `src/lib/` and `src/utils/` for utility functions
- **Types**: `src/types/` for TypeScript definitions

### Real-time Features
- Server-Sent Events endpoint: `/api/notifications/sse`
- Real-time balance updates, transaction notifications
- Live cashier dashboard updates
- Notification system with SSE integration

### Authentication Flow
- Multiple endpoints: `/api/auth/login`, `/api/auth/register`, `/api/auth/discord/*`
- Uses a login model instead of a specific page for logging in. 
- JWT-based sessions with email verification
- Discord OAuth integration with profile sync
- Password reset and account linking capabilities

### Development Patterns
- Uses React Server Components and Client Components appropriately
- TanStack Query for server state management
- Zustand stores for client state
- Custom hooks for feature-specific logic
- Service layer pattern for API interactions
- Component-based architecture with feature folders

### File Upload System
- Multiple upload endpoints: `/api/uploads/avatar`, `/api/uploads/[type]`
- Image processing with Sharp library
- Organized uploads in `public/uploads/` by type (avatars, deposits, news, products)
- V2 upload system with enhanced processing

### Important Notes
- The application serves ~1000 users with 30-40 concurrent (peaks of 100)
- Uses custom image processing and upload handling
- Implements comprehensive role-based access control
- Features extensive error handling and user feedback systems
- Maintains backwards compatibility across system updates