# Get-FileTree.ps1
# Script to generate a file tree layout of the Bank of Styx website
# Excludes node_modules and other commonly ignored files/folders
# Creates separate files for different directory groups to stay under 200 lines per file

# Define directories and files to exclude (based on .gitignore and common patterns)
$excludePatterns = @(
    ".git",
    "node_modules",
    ".pnpm-store",
    ".npm",

    "yarn.lock",
    ".next",
    "build",
    "dist",
    "out",
    ".env",
    ".env.local",
    ".env.development.local",
    ".env.test.local",
    ".env.production.local",
    ".env*.local",
    "*-debug.log*",
    "*-error.log*",
    ".idea",
    ".vscode",
    "*.swp",
    "*.swo",
    "*~",
    ".DS_Store",
    ".DS_Store?",
    "._*",
    ".Spotlight-V100",
    ".Trashes",
    "ehthumbs.db",
    "Thumbs.db",
    "coverage",
    "*.log",
    ".cache",
    ".temp",
    "*.bak",

    "*.tsbuildinfo",
    "public/sw.js",
    "public/workbox-*.js",
    "pnpm-lock.yaml"
)

# Function to check if a path should be excluded
function ShouldExclude {
    param (
        [string]$path
    )

    foreach ($pattern in $excludePatterns) {
        if ($pattern.Contains("*")) {
            # Handle wildcard patterns
            $regexPattern = "^" + [regex]::Escape($pattern).Replace("\*", ".*") + "$"
            if ($path -match $regexPattern) {
                return $true
            }
        } else {
            # Handle exact matches
            if ($path -eq $pattern -or $path.Contains("\$pattern\") -or $path.EndsWith("\$pattern")) {
                return $true
            }
        }
    }

    return $false
}

# Output file paths
$outputFolder = "C:\Users\<USER>\projects\test\Prompt\file-tree"
$mainOutputFile = "C:\Users\<USER>\projects\test\Prompt\website-file-tree.txt"

# Create output folder if it doesn't exist
if (-not (Test-Path -Path $outputFolder)) {
    New-Item -Path $outputFolder -ItemType Directory | Out-Null
    Write-Host "Created output folder: $outputFolder"
}

# Get the current directory
$rootDir = "C:\Users\<USER>\projects\test\web"

# Create a StringBuilder for better performance
$output = New-Object System.Text.StringBuilder

# Function to recursively get files and directories
function Get-FileTreeRecursive {
    param (
        [string]$directory,
        [string]$relativePath = ""
    )

    # Get all items in the current directory
    $items = Get-ChildItem -Path $directory -Force

    foreach ($item in $items) {
        $itemRelativePath = if ($relativePath) { "$relativePath\$($item.Name)" } else { $item.Name }

        # Check if the item should be excluded
        if (ShouldExclude -path $itemRelativePath) {
            continue
        }

        # Add the item to the output with full path
        $fullPath = $item.FullName

        if ($item.PSIsContainer) {
            # It's a directory
            [void]$output.AppendLine("$fullPath\")

            # Recursively process subdirectories
            Get-FileTreeRecursive -directory $item.FullName -relativePath $itemRelativePath
        } else {
            # It's a file
            [void]$output.AppendLine("$fullPath")
        }
    }
}

# Start the recursive process
Get-FileTreeRecursive -directory $rootDir

# Sort the output for better searchability
$sortedOutput = $output.ToString().Split("`n") | Where-Object { $_ -ne "" } | Sort-Object

# Create a StringBuilder for directories
$directoryOutput = New-Object System.Text.StringBuilder

# Function to recursively get directories only
function Get-DirectoriesRecursive {
    param (
        [string]$directory,
        [string]$relativePath = ""
    )

    # Get all items in the current directory
    $items = Get-ChildItem -Path $directory -Force | Where-Object { $_.PSIsContainer }

    foreach ($item in $items) {
        $itemRelativePath = if ($relativePath) { "$relativePath\$($item.Name)" } else { $item.Name }

        # Check if the item should be excluded
        if (ShouldExclude -path $itemRelativePath) {
            continue
        }

        # Add the directory to the output
        [void]$directoryOutput.AppendLine("$($item.FullName)\")

        # Recursively process subdirectories
        Get-DirectoriesRecursive -directory $item.FullName -relativePath $itemRelativePath
    }
}

# Start the recursive process for directories
Get-DirectoriesRecursive -directory $rootDir

# Sort the directory output
$sortedDirectories = $directoryOutput.ToString().Split("`n") | Where-Object { $_ -ne "" } | Sort-Object

# Create a new StringBuilder for the final output
$finalOutput = New-Object System.Text.StringBuilder
[void]$finalOutput.AppendLine("# Bank of Styx Website File Tree (Directories Only)")
[void]$finalOutput.AppendLine("# Generated on $(Get-Date)")
[void]$finalOutput.AppendLine("# Excludes node_modules, .git, and other commonly ignored files/folders")
[void]$finalOutput.AppendLine("# Root directory: $rootDir")
[void]$finalOutput.AppendLine("")

foreach ($line in $sortedDirectories) {
    [void]$finalOutput.Append("$line`n")
}

# Add a separator before the full file tree
[void]$finalOutput.AppendLine("")
[void]$finalOutput.AppendLine("# Full File Tree (Files and Directories)")
[void]$finalOutput.AppendLine("")

# Append the sorted full file tree to the final output
foreach ($line in $sortedOutput) {
    [void]$finalOutput.Append("$line`n")
}

# Save the complete output to the main file
$finalOutput.ToString() | Out-File -FilePath $mainOutputFile -Encoding utf8

Write-Host "Complete file tree has been saved to $mainOutputFile"
Write-Host "Total directories listed: $($sortedDirectories.Count)"
Write-Host "Total files and directories listed: $($sortedOutput.Count)"

# Now create separate files for different directory groups
# Define the directory groups to split into separate files
$directoryGroups = @(
    @{
        Name = "api-structure";
        Title = "API Structure";
        Pattern = "\\apps\\main-site\\src\\app\\api\\";
        Description = "API endpoints structure for the main site";
    },
    @{
        Name = "app-pages";
        Title = "App Pages";
        Pattern = "\\apps\\main-site\\src\\app\\(?!api)";
        Description = "Main site application pages";
    },
    @{
        Name = "components";
        Title = "Components";
        Pattern = "\\apps\\main-site\\src\\components\\";
        Description = "React components used in the application";
    },
    @{
        Name = "ui-package";
        Title = "UI Package";
        Pattern = "\\packages\\ui\\";
        Description = "Shared UI components package";
    },
    @{
        Name = "bank-modules";
        Title = "Bank Modules";
        Pattern = "\\apps\\main-site\\src\\app\\bank\\|\\apps\\main-site\\src\\app\\api\\bank\\";
        Description = "Bank-related modules and endpoints";
    },
    @{
        Name = "auth-modules";
        Title = "Auth Modules";
        Pattern = "\\apps\\main-site\\src\\app\\auth\\|\\apps\\main-site\\src\\app\\api\\auth\\";
        Description = "Authentication-related modules and endpoints";
    }
)

# Process each directory group
foreach ($group in $directoryGroups) {
    $groupOutput = New-Object System.Text.StringBuilder
    [void]$groupOutput.AppendLine("# Bank of Styx Website - $($group.Title)")
    [void]$groupOutput.AppendLine("# Generated on $(Get-Date)")
    [void]$groupOutput.AppendLine("# $($group.Description)")
    [void]$groupOutput.AppendLine("# Root directory: $rootDir")
    [void]$groupOutput.AppendLine("")

    # Filter directories for this group
    $groupDirectories = $sortedDirectories | Where-Object { $_ -match $group.Pattern }

    # Add directories to output
    foreach ($line in $groupDirectories) {
        [void]$groupOutput.Append("$line`n")
    }

    # Save to group-specific file
    $groupFilePath = Join-Path -Path $outputFolder -ChildPath "file-tree-$($group.Name).txt"
    $groupOutput.ToString() | Out-File -FilePath $groupFilePath -Encoding utf8

    Write-Host "Created group file: $groupFilePath with $($groupDirectories.Count) directories"
}

# Create a summary file with links to all the group files
$summaryOutput = New-Object System.Text.StringBuilder
[void]$summaryOutput.AppendLine("# Bank of Styx Website - File Tree Summary")
[void]$summaryOutput.AppendLine("# Generated on $(Get-Date)")
[void]$summaryOutput.AppendLine("# This file provides links to the different file tree sections")
[void]$summaryOutput.AppendLine("")
[void]$summaryOutput.AppendLine("## Available File Tree Sections")
[void]$summaryOutput.AppendLine("")

foreach ($group in $directoryGroups) {
    [void]$summaryOutput.AppendLine("### $($group.Title)")
    [void]$summaryOutput.AppendLine("$($group.Description)")
    [void]$summaryOutput.AppendLine("File: [file-tree-$($group.Name).txt](file-tree-$($group.Name).txt)")
    [void]$summaryOutput.AppendLine("")
}

[void]$summaryOutput.AppendLine("### Complete File Tree")
[void]$summaryOutput.AppendLine("The complete file tree with all files and directories")
[void]$summaryOutput.AppendLine("File: [$mainOutputFile]($mainOutputFile)")

# Save the summary file
$summaryFilePath = Join-Path -Path $outputFolder -ChildPath "README.md"
$summaryOutput.ToString() | Out-File -FilePath $summaryFilePath -Encoding utf8

Write-Host "Created summary file: $summaryFilePath"
Write-Host "File tree processing complete!"
